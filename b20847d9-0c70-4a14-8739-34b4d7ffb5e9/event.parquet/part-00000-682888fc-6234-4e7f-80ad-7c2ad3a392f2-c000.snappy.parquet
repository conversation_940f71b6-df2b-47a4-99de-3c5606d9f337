PAR1<@����	<  t   pending   start   finish �	j�����.  �<   �.d ds�$I� � ��X�y �y �y �y �y �y �y �y �-�����<�  �$   case_0
 1
 2
 3
 4
 5
 6
 7
 8
9d10 1 2 3 4 5 6 7 8n 2n 2n 2n 2n 2n 2n 2n 2n 2n 2n 3n 3n 3n 3n 3n 3n 3n 3n 3n 3n 4n 4n 4n 4n 4n 4n 4n 4n 4n 4n 5n 5n 5n 5n 5n 5n 5n 5n 5n 5n 6n 6n 6n 6n 6n 6n 6n 6n 6n 6n 7n 7n 7n 7n 7n 7n 7n 7n 7n 7n 8n 8n 8n 8n 8n 8n 8n 8n 8n 8n 9n 9n 9n 9n 9n 9n 9n 9n 9n99y� 0 1 2 3 4 5 6 7 8x 1x 1x 1x 1x 1x 1x 1x 1x 1x 1x 2x 2x 2x 2x 2x 2x 2x 2x 2x 2x 3x 3x 3x 3x 3x 3x 3x 3x 3x 3x 4x 4x 4x 4x 4x 4x 4x 4x 4x 4x 5x 5x 5x 5x 5x 5x 5x 5x 5x 5x 6x 6x 6x 6x 6x 6x 6x 6x 6x 6x 7x 7x 7x 7x 7x 7x 7x 7x 7x 7x 8x 8x 8x 8x 8x 8x 8x 8x 8x 8x 9x 9x 9x 9x 9x 9x 9x 9x 9x�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2��8248   case_249 �/��ҙ�
�.  ��   �. 	

 !"#$%&'()*+,-./01
	
 $%"#&' !/0-.1(+,)*


			


   $$$%%%"""###&&&'''   !!!///000---...111(((+++,,,)))***


�� �� �� �� &''�- ���23456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abc:;89Z[2^3_\]6b7c4`5aECD=><AB?@NOHIFGLMJKYPSTQRWXUV:::;;;888999ZZZ[[[222^^^333___\\\]]]666bbb777ccc444```555aaaEEECCCDDD===>>><<<AAABBB???@@@NNNOOOHHHIIIFFFGGGLLLMMMJJJKKKYYYPPPSSSTTTQQQRRRWWWXXXUUUVVV:::;;;888999ZZZ[[[222^^^333___�� �� 89�-�-2-���defghijklmnopqrstuvwxyz{|}~���������������������������������kjml��e�d�g�f�i�h�vuwnporqts��yx{z}|~����������������������������������kkkjjjmmmlll������eee���ddd���ggg���fff���iii���hhh���vvvuuuwwwnnnpppooorrrqqqtttsss������yyyxxx{{{zzz}}}|||~~~���������������������������������~� �gg�� �� J� �� b� ���S�������������������������������������������������ǰ���������������������ėÚƙŜ�Ǟ�������������������������������������������������������������������������������������ė����Ú����ƙ����Ŝ�������Ǟ�������������������������������������������������������������������������������������������������������������������������ė����Ú����ƙ����Ŝ�j� �������� �� ʖ ���������������������������������������������������������������������������������������������������������������������������y����������������������������������������������������������������������������������������������������������������������������������������������������������������� �� �� �� >� ������̿<  {@   提交工单М�务台处理   处室领导审批   运维人员�,H   系统内训师	EX8填报人确认 �
�	����	�.  �t   �.� q	H� A�	$H��@�	
0@B�$$H�@B	$	$� !A	�l�D�)R�H�"E��&R��lٲe�&2[6�"�	��$�&2�$8�-�H��֮e˖-[�. v	(׮]�v-۵kٲ		l׮e�$(��ڵlٮf�	��H� �	H AB� �	��@��		� ���-[6�� �		f)2[� �	��Rd6	 	���ڵk��
	$��-۵k׮]�	��[�k	 ��ٲ
�$$�� �%\ H%8	$HH
� $�	$��	�l"��̖M%\$f�-��l"Ef!7
�!Sٲ�)!n	"ײ]�v�Z�e��lײe�
� ]�lٮ]��	
�!7 A��)�	$$� !��	$$!J�A/A. A		$A)Rd�%e &	$���Md�l"�!J��!��lA. ��!o!/ k%e!A ���Z�kײ� [�ڵkײe��l��E ��%S)J%n�)n$�$HH�I�%SA��M���A�E�	� "!� MA �E�)S%J%n�)n$�,�e���Z�kٮf λ������.   �](   �.�  � � � � � � . � � � � � � � � � � R   � � � � � � � � � R  � � � � � � � � � R  � � � � � � � � � � 2 � � � � � � � � � � R � � � � � � � � � � R � � � � � � � � � � R � � � � >
 � � � � � � 2 ?
 � � � � � � � � � R @
 � � � � � � � � � R A
 � � � � � � � � � R B
 � � � � � � � � � � 2 � � � � � � � � � � R � � � � � � � � � � R � � � � � � � � � � R � � � � ~ � � � � � � 2  � � � � � � � � � R � � � � � � � � � � R � � � � � � � � � � R � � � � <>��<  $   提交
<处理   审批 ���³��.  �(;  �s�m�� X�? �? �? �? �� U� ��Z�ZUZZ�ZZ�U��� Z�8�UU���ZU�ZZU� T��U��ZZ��ZZU��Z��U�U�",U�Z���	ZUUQ �!Z@W�U���4�Z�ZZZZU��  �C������.   �!,<  d ds$I�� ��? �? �? �? X   � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � r  �C������.   �!,<  d ds$I�� ��? �? �? �? X   � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � r  �C������.   �!,<  d ds$I�� ��? �? �? �? X   � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � r finishstart  case_0case_99  填报人确认运维人员处理  �  �    处理提交�       �       �       � n�   ��   �-�
   �7�   �D�   �G�   �K�   �O�   �Hspark_schema %status% L   %case_id% L   %
event_name% L   %time %
resource_name% L   %resource_count %
fixed_cost %
resource_cost �.�&n5status�.�
�&n&6 (startfinish ,    �V�S4 &�5case_id�.�]�)&�&�6 (case_99case_0 ,    �W�S8 &�-5
event_name�.��&�-&�+6 (运维人员处理填报人确认 ,    �W�T` &�75 time�.���&�7<�  �   (�  �       �W�U. &�D5
resource_name�.��&�D&�C6�(提交处理 ,    �W�U8 &�G5 resource_count�.�C�&�G<      �(           �W�U0 &�K5 
fixed_cost�.�C�&�K<      �(           �X�V0 &�O5 
resource_cost�.�C�&�O<      �(           �X�V0 ҈�.&�S  \org.apache.spark.timeZone
Asia/Shanghai org.apache.spark.legacyINT96  org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"status","type":"string","nullable":true,"metadata":{}},{"name":"case_id","type":"string","nullable":true,"metadata":{}},{"name":"event_name","type":"string","nullable":true,"metadata":{}},{"name":"time","type":"integer","nullable":true,"metadata":{}},{"name":"resource_name","type":"string","nullable":true,"metadata":{}},{"name":"resource_count","type":"integer","nullable":true,"metadata":{}},{"name":"fixed_cost","type":"integer","nullable":true,"metadata":{}},{"name":"resource_cost","type":"integer","nullable":true,"metadata":{}}]} org.apache.spark.legacyDateTime  4parquet-mr version 1.12.2-prx (build ${buildNumber})�                 �  PAR1