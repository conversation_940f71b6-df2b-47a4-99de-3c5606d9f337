PAR1<@����	<  t   pending   start   finish L$�����  &0   �d ddb �-�����<�  �$   case_0
 1
 2
 3
 4
 5
 6
 7
 8
9d10 1 2 3 4 5 6 7 8n 2n 2n 2n 2n 2n 2n 2n 2n 2n 2n 3n 3n 3n 3n 3n 3n 3n 3n 3n 3n 4n 4n 4n 4n 4n 4n 4n 4n 4n 4n 5n 5n 5n 5n 5n 5n 5n 5n 5n 5n 6n 6n 6n 6n 6n 6n 6n 6n 6n 6n 7n 7n 7n 7n 7n 7n 7n 7n 7n 7n 8n 8n 8n 8n 8n 8n 8n 8n 8n 8n 9n 9n 9n 9n 9n 9n 9n 9n 9n99y� 0 1 2 3 4 5 6 7 8x 1x 1x 1x 1x 1x 1x 1x 1x 1x 1x 2x 2x 2x 2x 2x 2x 2x 2x 2x 2x 3x 3x 3x 3x 3x 3x 3x 3x 3x 3x 4x 4x 4x 4x 4x 4x 4x 4x 4x 4x 5x 5x 5x 5x 5x 5x 5x 5x 5x 5x 6x 6x 6x 6x 6x 6x 6x 6x 6x 6x 7x 7x 7x 7x 7x 7x 7x 7x 7x 7x 8x 8x 8x 8x 8x 8x 8x 8x 8x 8x 9x 9x 9x 9x 9x 9x 9x 9x 9x�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2�� 2��8248   case_249 ��	�����  ��u   � 	

 !"#$%&'()*+,-./01
	
 $%"#&' !/0-.1(+,)*
�2 �u23456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abc:;89Z[2^3_\]6b7c4`5aECD=><AB?@NOHIFGLMJKYPSTQRWXUV:;89Z[2^3_\]6b7c4`~2 �udefghijklmnopqrstuvwxyz{|}~���������������������������������kjml��e�d�g�f�i�h�vuwnporqts��yx{z}|~������������kjml��e~2 �u�������������������������������������������������ǰ���?������������������ėÚƙŜ�Ǟ�����������������������������������2 �u����������������������������������������������������������������������������������������������������������������������~2    �.����4�   �(   ��  � � � � � � .  � � � � � � � � � � 2 � � � � >
 � � � � � � 2 B
 � � � � � � � � � � 2 � � � � ~ � � � � � � 2 � � � � finishstart  case_0case_99  �  �    nT   ��
   ��   LHspark_schema %status% L   %case_id% L   %time �<&n5status���&n&6 (startfinish ,    � �4 &�5case_id��9�&�&�6 (case_99case_0 ,    � �8 &�5 time��/�&�<�  �   (�  �       �!� . �j�&�  \org.apache.spark.timeZone
Asia/Shanghai org.apache.spark.legacyINT96  org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"status","type":"string","nullable":true,"metadata":{}},{"name":"case_id","type":"string","nullable":true,"metadata":{}},{"name":"time","type":"integer","nullable":true,"metadata":{}}]} org.apache.spark.legacyDateTime  4parquet-mr version 1.12.2-prx (build ${buildNumber})<       6  PAR1