drop schema IF EXISTS "sp_oauth2" cascade ;
create schema IF NOT EXISTS sp_oauth2;
set schema sp_oauth2;

-- ----------------------------
-- Table structure for cloud_file_info
-- ----------------------------
DROP TABLE IF EXISTS `cloud_file_info`;
CREATE TABLE `cloud_file_info`  (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `file_name` varchar(64)  NULL DEFAULT NULL COMMENT '文件名称',
                                    `file_url` varchar(512)  NULL DEFAULT NULL COMMENT '云文件地址',
                                    `file_type` varchar(32)  NULL DEFAULT NULL COMMENT '文件类型',
                                    `deleted` tinyint(4) NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
                                    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                    `tenant_id` int(11) NULL DEFAULT NULL COMMENT '租户ID',
                                    `sheet_id` int(11) NULL DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for oauth_access_token
-- ----------------------------
DROP TABLE IF EXISTS `oauth_access_token`;
CREATE TABLE `oauth_access_token`  (
                                       `token_id` varchar(256)  NULL DEFAULT NULL,
                                       `token` blob NULL,
                                       `authentication_id` varchar(128)  NOT NULL,
                                       `user_name` varchar(256)  NULL DEFAULT NULL,
                                       `client_id` varchar(256) NULL DEFAULT NULL,
                                       `authentication` blob NULL,
                                       `refresh_token` varchar(256) NULL DEFAULT NULL,
                                       PRIMARY KEY (`authentication_id`)
) ENGINE = InnoDB CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for oauth_client_details
-- ----------------------------
DROP TABLE IF EXISTS `oauth_client_details`;
CREATE TABLE `oauth_client_details`  (
                                         `client_id` varchar(128)  NOT NULL,
                                         `resource_ids` varchar(256)  NULL DEFAULT NULL,
                                         `client_secret` varchar(256) NULL DEFAULT NULL,
                                         `scope` varchar(256)  NULL DEFAULT NULL,
                                         `authorized_grant_types` varchar(256)  NULL DEFAULT NULL,
                                         `web_server_redirect_uri` varchar(256)  NULL DEFAULT NULL,
                                         `authorities` varchar(256)  NULL DEFAULT NULL,
                                         `access_token_validity` int(11) NULL DEFAULT NULL,
                                         `refresh_token_validity` int(11) NULL DEFAULT NULL,
                                         `additional_information` varchar(4096)  NULL DEFAULT NULL,
                                         `autoapprove` varchar(256) NULL DEFAULT NULL,
                                         PRIMARY KEY (`client_id`)
) ENGINE = InnoDB CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for oauth_client_token
-- ----------------------------
DROP TABLE IF EXISTS `oauth_client_token`;
CREATE TABLE `oauth_client_token`  (
                                       `token_id` varchar(256)  NULL DEFAULT NULL,
                                       `token` blob NULL,
                                       `authentication_id` varchar(128)  NOT NULL,
                                       `user_name` varchar(256)  NULL DEFAULT NULL,
                                       `client_id` varchar(256)  NULL DEFAULT NULL,
                                       PRIMARY KEY (`authentication_id`)
) ENGINE = InnoDB CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for oauth_refresh_token
-- ----------------------------
DROP TABLE IF EXISTS `oauth_refresh_token`;
CREATE TABLE `oauth_refresh_token`  (
                                        `token_id` varchar(256)  NULL DEFAULT NULL,
                                        `token` blob NULL,
                                        `authentication` blob NULL
) ENGINE = InnoDB CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_admin
-- ----------------------------
DROP TABLE IF EXISTS `t_admin`;
CREATE TABLE `t_admin`  (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `user_name` varchar(128)  NULL DEFAULT NULL,
                            `name` varchar(128)  NULL DEFAULT NULL,
                            `password` varchar(128)  NULL DEFAULT NULL,
                            `level` varchar(12)  NULL DEFAULT NULL COMMENT 'USER,ANALYST,ROOT',
                            `deleted` tinyint(4) NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
                            `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                            `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                            `status` tinyint(2) NULL DEFAULT 1 COMMENT '1：正常，2：停用',
                            `tenant_id` int(11) NULL DEFAULT NULL,
                            user_source varchar(64) null default 'LOCAL'  comment '用户来源',
                            PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8  COMMENT = '用户信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_admin_license
-- ----------------------------
DROP TABLE IF EXISTS `t_admin_license`;
CREATE TABLE `t_admin_license`  (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `sn` varchar(64)  NULL DEFAULT '',
                                    `filename` varchar(64)   NOT NULL DEFAULT '',
                                    `not_before` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `not_after` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `consumer_type` varchar(64)NULL DEFAULT NULL,
                                    `consumer_amount` int(11) NULL DEFAULT 0,
                                    `capacity` bigint(20) NOT NULL DEFAULT 0,
                                    `info` varchar(64)  NOT NULL DEFAULT '',
                                    `status` tinyint(4) NULL DEFAULT 0,
                                    `deleted` tinyint(4) NULL DEFAULT 0,
                                    `created` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `updated` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                    `tenant_id` int(11) NOT NULL DEFAULT 0,
                                    PRIMARY KEY (`id`) ,
                                    UNIQUE INDEX `sn`(`sn`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_buried_point
-- ----------------------------
DROP TABLE IF EXISTS `t_buried_point`;
CREATE TABLE `t_buried_point`  (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `type` int(11) NULL DEFAULT NULL COMMENT '埋点类型',
                                   `source` int(11) NULL DEFAULT NULL COMMENT '埋点类型',
                                   `user` varchar(64) NULL DEFAULT NULL COMMENT '用户',
                                   `token` varchar(128)  NULL DEFAULT NULL COMMENT 'token值',
                                   `spend_time` varchar(64) NULL DEFAULT '-1' COMMENT '花费时间',
                                   `info` text  NULL COMMENT '信息值',
                                   `state` int(11) NULL DEFAULT NULL COMMENT '状态',
                                   `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                   `tenant_id` int(11) NULL DEFAULT NULL COMMENT '租户ID',
                                   PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 25353 CHARACTER SET = utf8  COMMENT = '埋点日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_authority
-- ----------------------------
DROP TABLE IF EXISTS `t_data_authority`;
CREATE TABLE `t_data_authority`  (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `data_id` varchar(32)  NULL DEFAULT NULL COMMENT '数据Id',
                                     `data_type` int(11) NULL DEFAULT NULL COMMENT '数据类型，1：topic',
                                     `deleted` tinyint(4) NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
                                     `create_user` int(11) NULL DEFAULT NULL COMMENT '创建人Id',
                                     `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                     `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                     PRIMARY KEY (`id`) ,
                                     UNIQUE INDEX `t_data_authority_data_id_data_type_uindex`(`data_id`, `data_type`)
) ENGINE = InnoDB AUTO_INCREMENT = 317 CHARACTER SET = utf8  COMMENT = '数据权限' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dictionaries
-- ----------------------------
DROP TABLE IF EXISTS `t_dictionaries`;
CREATE TABLE `t_dictionaries`  (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `type` int(11) NULL DEFAULT NULL COMMENT '字典类型: 1:发送统计周报邮箱',
                                   `indexes` varchar(32) NULL DEFAULT NULL COMMENT '索引',
                                   `value` varchar(521)  NULL DEFAULT NULL COMMENT '值:多个值时以 &@ 分割',
                                   `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                   `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                   `status` tinyint(2) NULL DEFAULT 1 COMMENT '1：正常，2：停用',
                                   PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8  COMMENT = '字典' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_gateway_path_param
-- ----------------------------
DROP TABLE IF EXISTS `t_gateway_path_param`;
CREATE TABLE `t_gateway_path_param`  (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `path` varchar(255)   NULL DEFAULT NULL COMMENT '接口路径',
                                         `param` varchar(255)  NULL DEFAULT NULL COMMENT '参数名称（检查租户合法性）',
                                         `table_name` varchar(255)  NULL DEFAULT NULL COMMENT '请求表名称',
                                         `field` varchar(255)  NULL DEFAULT NULL COMMENT '表字段',
                                         `service_id` int(11) NULL DEFAULT NULL COMMENT 'sp-engine=100,data-model=200,execution=300',
                                         `service_name` varchar(255) NULL DEFAULT NULL COMMENT '服务名称',
                                         `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `method` varchar(255) NULL DEFAULT NULL COMMENT '请求方式,GET/POST',
                                         `deleted` tinyint(2) NULL DEFAULT 0,
                                         PRIMARY KEY (`id`)
)  CHARACTER SET = utf8 ;

-- ----------------------------
-- Table structure for t_group
-- ----------------------------
DROP TABLE IF EXISTS `t_group`;
CREATE TABLE `t_group`  (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `company` varchar(255)  NULL DEFAULT NULL,
                            `email` varchar(255)  NULL DEFAULT NULL,
                            `phone` varchar(255)  NULL DEFAULT NULL,
                            `password` varchar(255)  NULL DEFAULT NULL,
                            `datum_library_process_id` text  NULL DEFAULT NULL,
                            `expiration` datetime(0) NULL DEFAULT NULL,
                            `status` int(4) NULL DEFAULT NULL COMMENT '状态（10：申请中，20：驳回，30：已开通，40：重复申请，50：已过期）',
                            `type` tinyint(255) NULL DEFAULT NULL COMMENT '1:用户申请，2：后台申请',
                            `deleted` tinyint(2) NULL DEFAULT 0,
                            `versions` int(11) NULL DEFAULT NULL COMMENT '版本号',
                            `employees_number` int(11) NULL DEFAULT NULL COMMENT '员工数量',
                            `space_size` bigint(20) NULL DEFAULT NULL COMMENT '空间大小MB',
                            `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                            `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                            PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 92 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_group_contact
-- ----------------------------
DROP TABLE IF EXISTS `t_group_contact`;
CREATE TABLE `t_group_contact`  (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `source` int(11) NULL DEFAULT 0 COMMENT '数据来源，：1，延长有效期，2，升级版本',
                                    `state` int(11) NULL DEFAULT 0 COMMENT '状态：0 未处理，1已处理',
                                    `remark` varchar(512)  NULL DEFAULT NULL COMMENT '备注',
                                    `group_id` int(11) NULL DEFAULT NULL,
                                    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                    PRIMARY KEY (`id`)
) CHARACTER SET = utf8 ;

-- ----------------------------
-- Table structure for t_license
-- ----------------------------
DROP TABLE IF EXISTS `t_license`;
CREATE TABLE `t_license`  (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `company` varchar(64)   NOT NULL DEFAULT '',
                              `tel` varchar(32)   NOT NULL DEFAULT '',
                              `email` varchar(128)   NOT NULL DEFAULT '',
                              `not_before` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `not_after` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `capacity` bigint(20) NOT NULL DEFAULT 0,
                              `consumer_type` varchar(64)  NOT NULL DEFAULT '',
                              `consumer_amount` int(11) NOT NULL DEFAULT 1,
                              `subject` varchar(128)   NOT NULL DEFAULT '',
                              `info` varchar(128)   NOT NULL DEFAULT '',
                              `status` tinyint(4) NOT NULL DEFAULT 0,
                              `deleted` tinyint(4) NOT NULL DEFAULT 0,
                              `created` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `updated` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                              PRIMARY KEY (`id`) ,
                              INDEX `info`(`info`)
) CHARACTER SET = utf8;

-- ----------------------------
-- Table structure for t_official_website_news
-- ----------------------------
DROP TABLE IF EXISTS `t_official_website_news`;
CREATE TABLE `t_official_website_news`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `type` varchar(32)  NULL DEFAULT NULL,
                                            `info` text  NULL COMMENT '内容',
                                            `abstract_str` text  NULL COMMENT '摘要',
                                            `title` varchar(256)  NULL DEFAULT NULL COMMENT '标题',
                                            `news_time` varchar(32)  NULL DEFAULT NULL COMMENT '新闻时间',
                                            `drinking_address` varchar(256)  NULL DEFAULT NULL COMMENT '引用地址',
                                            `image` varchar(256)  NULL DEFAULT NULL COMMENT '图片地址',
                                            `newsTime` varchar(128)  NULL DEFAULT NULL COMMENT '新闻时间',
                                            `status` int(11) NULL DEFAULT NULL COMMENT '状态，1：展示，2：不展示',
                                            `company` varchar(32)  NULL DEFAULT NULL COMMENT '用户所属公司',
                                            `deleted` tinyint(4) NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
                                            `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                            `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                            PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8  COMMENT = '官网信息新闻' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_sp_user
-- ----------------------------
DROP TABLE IF EXISTS `t_sp_user`;
CREATE TABLE `t_sp_user`  (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `email` varchar(255)  NULL DEFAULT NULL,
                              `username` varchar(255)  NULL DEFAULT NULL,
                              `password` varchar(255)   NULL DEFAULT NULL,
                              `status` varchar(255) NULL DEFAULT '1' COMMENT '1：启用，2：禁用',
                              `deleted` varchar(255)  NULL DEFAULT '0',
                              `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                              `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                              PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_user_data_authority
-- ----------------------------
DROP TABLE IF EXISTS `t_user_data_authority`;
CREATE TABLE `t_user_data_authority`  (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `data_authority_id` int(11) NULL DEFAULT NULL COMMENT '数据权限ID',
                                          `user_id` int(11) NULL DEFAULT NULL COMMENT '用户Id',
                                          `authority_value` int(11) NULL DEFAULT NULL COMMENT '权限值：2：读权限，4：修改权限，8：分配权限；具体的值是权限之和',
                                          `create_user` int(11) NULL DEFAULT NULL COMMENT '创建人Id',
                                          `deleted` tinyint(4) NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
                                          `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
                                          `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                          PRIMARY KEY (`id`) ,
                                          UNIQUE INDEX `t_user_data_authority_user_id_uindex`(`user_id`, `data_authority_id`, `deleted`) ,
                                          INDEX `t_user_data_authority_t_data_authority__fk`(`data_authority_id`) ,
                                          CONSTRAINT `t_user_data_authority_t_data_authority__fk` FOREIGN KEY (`data_authority_id`) REFERENCES `t_data_authority` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 321 CHARACTER SET = utf8  COMMENT = '用户数据权限' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO oauth_client_details (client_id, resource_ids, client_secret, scope, authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, refresh_token_validity, additional_information, autoapprove) VALUES ('client', '', '$2a$10$at41IlZmMnUO5EyjDFRb3OUUdP5pdZOIrOzaO.VMxdHMZgNrepz6.', 'app', 'password', 'https://www.baidu.com', null, 86400, 864000, null, null);

INSERT INTO `t_group`(`id`, `company`, `email`, `phone`, `password`, `expiration`, `status`, `type`,
                                  `deleted`, `versions`, `employees_number`, `space_size`, `create_time`, `update_time`)
VALUES (1, '有限公司', '<EMAIL>', '18888888888', '18d489181d25f2e38c7e943db24595e6',
        '2050-10-31 00:00:00', 30, 2, 0, 3000, 200, 10240000, '2022-07-02 19:42:08', '2022-08-28 10:28:12');

INSERT INTO `t_admin`(`id`, `user_name`,`name`, `password`, `level`, `deleted`, `create_time`,
                                  `update_time`, `status`, `tenant_id`)
VALUES (1, '<EMAIL>','adminName' ,'18d489181d25f2e38c7e943db24595e6', 'ROOT', 0, '2022-08-27 00:18:14', NULL, 1, 1);

INSERT INTO `t_admin`(`id`, `user_name`,`name`, `password`, `level`, `deleted`, `create_time`,
                                  `update_time`, `status`, `tenant_id`)
VALUES (2, '<EMAIL>','userName' ,'18d489181d25f2e38c7e943db24595e6', 'USER', 0, '2022-08-27 00:18:14', NULL, 1, 1);

INSERT INTO `t_admin`(`id`, `user_name`,`name`, `password`, `level`, `deleted`, `create_time`,
                                  `update_time`, `status`, `tenant_id`)
VALUES (3, '<EMAIL>','tenant2' ,'18d489181d25f2e38c7e943db24595e6', 'ROOT', 0, '2022-08-27 00:18:14', NULL, 1, 2);



ALTER TABLE t_user_data_authority ADD COLUMN `filter` varchar(5000)  DEFAULT NULL;




alter table t_admin
    add authorization_code varchar(512) null comment '授权编码';

alter table t_admin
    add authorization_date datetime null;


CREATE TABLE `t_user_group` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `name` varchar(255) DEFAULT NULL,
                                `tenant_id` int(11) DEFAULT NULL,
                                `create_user` int(11) DEFAULT NULL,
                                `deleted` tinyint(4) DEFAULT '0' COMMENT '0：未删除，1：已删除',
                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ;

CREATE TABLE `t_user_group_detail` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `user_group_id` int(11) DEFAULT NULL,
                                       `user_id` int(11) DEFAULT NULL,
                                       `tenant_id` int(11) DEFAULT NULL,
                                       `create_user` int(11) DEFAULT NULL,
                                       `deleted` tinyint(4) DEFAULT '0' COMMENT '0：未删除，1：已删除',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                       `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ;

alter table t_data_authority add `parent_id` int DEFAULT NULL COMMENT '父ID';

ALTER TABLE t_user_data_authority add `user_group_id` int DEFAULT NULL;

CREATE TABLE `t_gateway_path_permission` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `path` varchar(255)  DEFAULT NULL COMMENT '接口路径',
                                             `param` varchar(255)  DEFAULT NULL COMMENT '参数名称',
                                             `service_id` int(11) DEFAULT NULL COMMENT 'sp-engine=100,data-model=200,execution=300',
                                             `service_name` varchar(255)   DEFAULT NULL COMMENT '服务名称',
                                             `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                             `method` varchar(255) DEFAULT NULL COMMENT '请求方式,GET/POST',
                                             `deleted` tinyint(4) DEFAULT '0',
                                             `permission_type` tinyint(4) DEFAULT NULL COMMENT '鉴权类型（10-浏览，20-编辑，30-权限设置）',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci ROW_FORMAT=DYNAMIC;


INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicFilterList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/selectNewSheetInfo', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicSheetData', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getVariableKpiDataV3', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/kpiCalculate', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicProcessKpiList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/processTree', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getReworkList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicSheetKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getSheetRelatedVariables4select', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicSheetDeviation', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/queryRootCauseCalResult', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicSheetCaseView', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getCaseViewDetail', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getConformanceGeneralData', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getConformanceKpi4General', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getConformanceViewData', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getUnConformanceList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/conformance/getAllowProcess', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/selectNewSheetInfo', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sheet/getThroughputTimeList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getEventDurationList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getAggCalcTimeView', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getCaseTotalCurrent', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getVariableValueList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getEventList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getAggReworkView', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sheet/getContrastProcessTree', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getKnowledgeDataList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicKpiSetList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicParamSetList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getKnowledgeDataDetail', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);




INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getActionFlowList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getActionNode', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sheet/getSnapshotLog', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getActionFlowLogs', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getActionStartParam', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getRunFlowAction', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/getCurrentActionCode', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/saveActionFlowList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/saveActionNode', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/action/runActionFlow', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);



INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getSimulationProgramme', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getSimulationGatewayByProgramme', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getSimulationRun', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getSimulationResource', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getSimulationEventByProgramme', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/getRunSimulationResult', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);









INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataPoolDetail', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataConnectorList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataExactorList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataTaskList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataTaskChildList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-data/getTableInfoListByPoolId', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-data/getTransformationSqlById', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataModelList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataModelDetailNonRelated', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataModelLoadData', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataModelDetailHasRelated', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getDataPoolFileList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getFileDataList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getTableListByDataConnector', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getJdbcTableFieldList', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getPreviewData', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 10);








INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/addTopicSheet', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/updateTopicData', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteBusinessTopic', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveTopicFilterV2', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteTopicFilterList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 10);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveBookMark', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/loadBookMark', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteBookMark', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/clearFilter', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveTopicKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteTopicKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveTopicParam', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteTopicParam', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/updateTopicProcessKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteProcessTreeKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/getTopicProcessKpiList', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveSheetComponent', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteSheetComponent', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/deleteTopicSheet', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/saveKpi', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/conformance/convertVariantToBpmn', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/setConformance', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/removeConformance', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-engine/sheetRelease', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);



INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/settingSimulationRunCost', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/settingSimulationRunTime', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/createOrUpdateSimulationRun', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/deleteSimulationRun', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/updateSimulationResource', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/simulation/runSimulation', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 20);






INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/setFieldType', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/testJdbcConnector', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveJdbcConnector', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/getJdbcConnectorEditInfo', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/editJdbcConnector', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deleteDataConnector', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deletePoolFile', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveDataExtraction', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveTableFieldAndType', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deleteDataExtrator', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveDataTask', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveOrUpdateDataTaskChild', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-job/runJobDataTaskExtracts', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/updateDataTaskChildSort', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deleteDataTaskChild', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/sp-job/runTransformation', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/addFile4DataModel', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/removeCaseTable', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/setCaseTable', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deleteDataModelFile', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model-site/deleteDataModelSite', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/addForeignKey', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/asyncLoadData', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/saveDataModel', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`,  `service_id`, `service_name`, `create_time`, `method`, `deleted`, `permission_type`) VALUES ( '/data-model/deleteDataModel', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);

INSERT INTO `t_gateway_path_permission`(`path`, `param`, `service_id`, `service_name`, `create_time`,
                                                    `method`, `deleted`, `permission_type`)
VALUES ('/file/fileUpload', 'poolId', 200, 'sp-data-merge', now(), 'POST', 0, 20);



INSERT INTO `t_gateway_path_permission`(`path`, `param`, `service_id`, `service_name`, `create_time`,
                                                    `method`, `deleted`, `permission_type`)
VALUES ('/sp-engine/createOrUpdateBatch', 'topicId', 100, 'sp-engine', now(), 'POST', 0, 30);


alter table t_admin
    add sso_id varchar(128) null after tenant_id;





DROP TABLE IF EXISTS `t_sso_group_mapper`;
CREATE TABLE `t_sso_group_mapper`
(
    id          int auto_increment
        primary key,
    name        varchar(255) NULL DEFAULT NULL comment '名称',
    rule        varchar(1024) NULL comment '规则',
    group_info  text NULL comment '组信息',
    deleted     tinyint  default 0 null comment '0：未删除，1：已删除',
    create_time datetime default CURRENT_TIMESTAMP null,
    update_time datetime null on update CURRENT_TIMESTAMP,
    tenant_id   int null comment '租户ID'
) ENGINE = InnoDB CHARACTER SET = utf8;


INSERT INTO oauth_client_details (client_id, resource_ids, client_secret, scope, authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, refresh_token_validity, additional_information, autoapprove) VALUES ('oidcOauth', null, '$2a$10$at41IlZmMnUO5EyjDFRb3OUUdP5pdZOIrOzaO.VMxdHMZgNrepz6.', 'openid,login', 'authorization_code', 'http://localhost:8888/login/oauth2/code/prx', null, null, null, null, null);



-- 2.8.8
-- auto-generated definition
create table t_process_manage
(
    id                     int auto_increment
        primary key,
    process_name           varchar(255)                         null,
    level                  int                                  null,
    parent_id              int        default 0                 null ,
    release_flag           tinyint(1) default 0                 null ,
    discard_flag           tinyint(1) default 0                 null ,
    process_code           varchar(255)                         null ,
    responsible_department varchar(255)                         null ,
    version_code           varchar(255)                         null ,
    file_id                int        default 0                 null ,
    deleted                int        default 0                 null ,
    release_time           datetime                             null ,
    create_time            datetime   default CURRENT_TIMESTAMP null,
    update_time            datetime                             null on update CURRENT_TIMESTAMP,
    tenant_id              int                                  null,
    best_practice_flag     tinyint(1) default 0                 null ,
    responsible_person_id  varchar(255)                         null ,
    description            text                                 null ,
    datum_library_flag     tinyint(1) default 0                 null
);




-- 2.8.9

alter table cloud_file_info
    add local_storage tinyint(1) default 0 null;



alter table t_process_manage
    add scope varchar(500) null;

alter table t_process_manage
    add business_objectives varchar(500) null;


alter table t_process_manage
    add kpi_indicators varchar(500) null;

alter table t_process_manage
    add remarks varchar(500) null;

alter table t_process_manage
    add draft_flag tinyint(1) default 0 null;

alter table t_process_manage
    add operation_monitoring varchar(100) null;


alter table t_process_manage
    add process_input varchar(100) null;

alter table t_process_manage
    add process_output varchar(100) null;

alter table t_process_manage
    add design_it_systems varchar(100) null;

alter table t_process_manage
    add critical_control_points varchar(100) null;

UPDATE t_gateway_path_permission set deleted = 1 where path = '/sp-engine/getSheetRelatedVariables4select';

ALTER TABLE t_group ADD COLUMN model_count_limit int(11) DEFAULT NULL;
ALTER TABLE t_license ADD COLUMN model_count_limit int(11) DEFAULT NULL;

CREATE TABLE `t_admin_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `role_id` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_user` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
CREATE TABLE `t_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `level` tinyint DEFAULT '1',
  `create_user` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
CREATE TABLE `t_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `level` int DEFAULT '0',
  `tag` varchar(255) DEFAULT NULL,
  `index_tag` int DEFAULT NULL,
  `description` varchar(255)  DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `create_user` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  `parent_id` int DEFAULT '0',
  `has_children` tinyint DEFAULT '0',
  `hidden` tinyint(4) DEFAULT '0',
  `sorting` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
CREATE TABLE `t_role_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int DEFAULT NULL,
  `permission_id` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_user` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

ALTER TABLE t_admin add COLUMN role_expire TINYINT(4) DEFAULT '0';

INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (1, '指挥舱', 0, 'getTopicModuleList', 1000, NULL, NULL, 1, '2023-09-28 14:11:42', 0, 1, 0, 1, 0, 1);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (2, '查看权限', 1, 'getTopicModuleList', 1000, NULL, NULL, 1, '2023-09-28 14:12:14', 0, 1, 1, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (3, '分享权限', 1, 'shareSheetPage', 1002, NULL, NULL, 1, '2023-09-28 14:12:35', 0, 1, 1, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (4, '工作台', 0, '/sp-engine/getBusinessTopicList', 2000, NULL, NULL, 1, '2023-09-28 14:12:55', 0, 1, 0, 1, 0, 2);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (5, '查看权限', 1, '/sp-engine/getBusinessTopicList', 2000, NULL, NULL, 1, '2023-09-28 14:13:41', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (7, '创建业务分析', 1, '/sp-engine/saveBusinessTopic', 2001, NULL, NULL, 1, '2023-09-28 14:14:21', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (8, '权限设置', 1, '/user-data-authority/createOrUpdateBatch', 2002, NULL, NULL, 1, '2023-09-28 14:14:33', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (9, '下载', 1, '/sp-engine/exportOLAP', 2004, NULL, NULL, 1, '2023-09-28 14:14:48', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (12, '创建执行动作', 1, '/action/createActionFlow', 2100, NULL, NULL, 1, '2023-10-09 05:20:49', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (15, '创建流程仿真', 1, '/simulation/createOrUpdateSimulation', 2300, NULL, NULL, 1, '2023-10-09 05:22:09', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (18, '执行优化', 0, 'execteAction', 3000, NULL, NULL, 1, '2023-10-09 05:22:59', 0, 1, 0, 0, 0, 4);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (24, '流程管理', 0, 'processManager', 4000, NULL, NULL, 1, '2023-10-09 05:26:32', 0, 1, 0, 1, 0, 3);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (25, '查看流程管理', 1, '/sp-process/getProcessManageList', 4001, NULL, NULL, 1, '2023-10-09 05:26:49', 0, 1, 24, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (26, '创建一级流程', 1, '/sp-process/createOrUpdateProcess', 4002, NULL, NULL, 1, '2023-10-09 05:27:05', 0, 1, 24, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (27, '发布', 1, '/sp-process/releaseProcessManage', 4003, NULL, NULL, 1, '2023-10-09 05:27:30', 0, 1, 24, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (28, '下载BPMN', 1, 'processManagerExportBPMN', 4004, NULL, NULL, 1, '2023-10-09 05:27:45', 0, 1, 24, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (29, '下载SVG', 1, 'processManagerExportSVG', 4005, NULL, NULL, 1, '2023-10-09 05:27:59', 0, 1, 24, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (33, '数据融合', 0, 'dataMerge', 5000, NULL, NULL, 1, '2023-10-09 05:29:06', 0, 1, 0, 1, 0, 5);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (34, '查看数据融合', 1, '/data-model/getDataPoolList', 5001, NULL, NULL, 1, '2023-10-09 05:29:26', 0, 1, 33, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (35, '导入数据', 1, '/file/fileUpload', 5002, NULL, NULL, 1, '2023-10-09 05:29:47', 0, 1, 33, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (37, '查看数据链接', 1, '/data-model/getDataConnectorList', 5100, NULL, NULL, 1, '2023-10-09 05:30:22', 0, 1, 33, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (49, '用户设置管理', 0, 'userManager', 6000, NULL, NULL, 1, '2023-10-09 05:34:02', 0, 1, 0, 1, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (51, '创建用户', 1, '/admin/create', 6001, NULL, NULL, 1, '2023-10-09 05:34:42', 0, 1, 49, 0, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (52, '停用用户', 1, '/admin/pauseUser', 6004, NULL, NULL, 1, '2023-10-09 05:34:58', 0, 1, 49, 0, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (53, '启用用户', 1, '/admin/startUser', 6003, NULL, NULL, 1, '2023-10-09 05:35:16', 0, 1, 49, 0, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (54, '修改用户', 1, '/admin/update', 6002, NULL, NULL, 1, '2023-10-09 05:35:31', 0, 1, 49, 0, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (56, '删除用户', 1, '/admin/deleteUser', 6005, NULL, NULL, 1, '2023-10-09 05:36:04', 0, 1, 49, 0, 1, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (62, '创建知识模型', 1, '/sp-engine/saveBusinessTopic', 2200, NULL, NULL, 1, '2023-10-09 05:59:37', 0, 1, 4, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (63, '数据模型', 1, '/data-model/getDataModelList', 5200, NULL, NULL, 1, '2023-10-09 06:02:54', 0, 1, 33, 0, 0, NULL);
INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`) VALUES (64, '发布业务分析', 1, 'topicRelease', 2003, NULL, NULL, 1, '2023-10-09 06:44:11', 0, 1, 4, 0, 0, NULL);


INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (90, 1, 1, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (91, 1, 2, '2023-09-28 14:20:42', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (92, 1, 3, '2023-09-28 14:20:52', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (93, 1, 4, '2023-09-28 14:21:00', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (94, 1, 5, '2023-09-28 14:21:05', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (95, 1, 6, '2023-09-28 14:21:11', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (96, 1, 7, '2023-09-28 14:21:19', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (97, 1, 8, '2023-09-28 14:21:24', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (98, 1, 9, '2023-09-28 14:21:36', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (99, 1, 10, '2023-09-28 14:21:42', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (100, 1, 11, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (101, 1, 12, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (102, 1, 13, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (103, 1, 14, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (104, 1, 15, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (105, 1, 16, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (106, 1, 17, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (107, 1, 18, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (108, 1, 19, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (109, 1, 11, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (110, 1, 20, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (111, 1, 21, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (112, 1, 22, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (113, 1, 23, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (114, 1, 24, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (115, 1, 25, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (116, 1, 26, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (117, 1, 27, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (118, 1, 28, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (119, 1, 29, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (120, 1, 30, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (121, 1, 31, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (122, 1, 32, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (123, 1, 33, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (124, 1, 34, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (125, 1, 35, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (126, 1, 36, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (127, 1, 36, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (128, 1, 37, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (129, 1, 38, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (130, 1, 39, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (131, 1, 40, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (132, 1, 41, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (133, 1, 42, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (134, 1, 43, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (135, 1, 44, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (136, 1, 45, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (137, 1, 46, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (138, 1, 47, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (139, 1, 48, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (140, 1, 49, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (141, 1, 50, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (142, 1, 51, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (143, 1, 52, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (144, 1, 53, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (145, 1, 54, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (146, 1, 55, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (147, 1, 56, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (148, 1, 57, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (149, 1, 58, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (150, 1, 59, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (151, 1, 60, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (152, 1, 61, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (153, 1, 62, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (154, 1, 63, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (155, 1, 64, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (791, 2, 25, '2023-10-12 10:22:15', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (792, 2, 2, '2023-10-12 10:22:15', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (793, 4, 24, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (794, 4, 25, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (795, 4, 26, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (796, 4, 27, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (797, 4, 28, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (798, 4, 29, '2023-10-12 10:22:42', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (799, 6, 64, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (800, 6, 1, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (801, 6, 33, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (802, 6, 2, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (803, 6, 34, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (804, 6, 3, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (805, 6, 35, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (806, 6, 4, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (807, 6, 5, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (808, 6, 37, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (809, 6, 7, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (810, 6, 8, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (811, 6, 9, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (812, 6, 12, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (813, 6, 15, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (814, 6, 18, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (815, 6, 25, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (816, 6, 28, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (817, 6, 29, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (818, 6, 62, '2023-10-12 10:23:13', NULL, 0, 1);
INSERT INTO `t_role_permission`(`id`, `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (819, 6, 63, '2023-10-12 10:23:13', NULL, 0, 1);


INSERT INTO `t_role`(`id`, `name`, `level`, `create_user`, `create_time`, `deleted`, `tenant_id`) VALUES (1, 'ROOT', 0, 1, '2023-09-28 14:11:10', 0, 1);
INSERT INTO `t_role`(`id`, `name`, `level`, `create_user`, `create_time`, `deleted`, `tenant_id`) VALUES (2, '用户', 1, 1, '2023-09-28 14:11:20', 0, 1);
INSERT INTO `t_role`(`id`, `name`, `level`, `create_user`, `create_time`, `deleted`, `tenant_id`) VALUES (4, '流程管理员', 1, NULL, '2023-09-28 17:46:22', 0, 1);
INSERT INTO `t_role`(`id`, `name`, `level`, `create_user`, `create_time`, `deleted`, `tenant_id`) VALUES (6, '数据分析员', 1, NULL, '2023-10-11 17:45:20', 0, 1);

INSERT INTO `t_admin_role`(`id`, `user_id`, `role_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES (1, 1, 1, '2023-09-28 14:19:40', 1, 0, 1);



alter table t_user_group
    add sso_id varchar(5000) null;

INSERT INTO `t_admin`( `user_name`, `password`, `level`, `deleted`, `user_source`, `create_time`, `update_time`, `status`, `tenant_id`, `sso_id`, `authorization_code`, `authorization_date`, `role_expire`) VALUES ( 'ROOT@1', 'cc7a301a615349f08524e5ef15f663e8', 'SUPER', 0, 'LOCAL', '2023-11-02 15:40:45', '2023-11-02 15:41:52', 1, 1, NULL, NULL, NULL, 0);

ALTER TABLE t_permission ADD COLUMN `i18n_no` varchar(64) DEFAULT NULL;

UPDATE `t_permission` SET  `i18n_no` = 300400 WHERE `id` = 1;
UPDATE `t_permission` SET  `i18n_no` = 300401 WHERE `id` = 2;
UPDATE `t_permission` SET  `i18n_no` = 300402 WHERE `id` = 3;
UPDATE `t_permission` SET  `i18n_no` = 300500 WHERE `id` = 4;
UPDATE `t_permission` SET  `i18n_no` = 300501 WHERE `id` = 5;
UPDATE `t_permission` SET  `i18n_no` = 300502 WHERE `id` = 7;
UPDATE `t_permission` SET  `i18n_no` = 300503 WHERE `id` = 8;
UPDATE `t_permission` SET  `i18n_no` = 300504 WHERE `id` = 9;
UPDATE `t_permission` SET  `i18n_no` = 300505 WHERE `id` = 12;
UPDATE `t_permission` SET  `i18n_no` = 300506 WHERE `id` = 15;
UPDATE `t_permission` SET  `i18n_no` = 300700 WHERE `id` = 18;
UPDATE `t_permission` SET  `i18n_no` = 300600 WHERE `id` = 24;
UPDATE `t_permission` SET  `i18n_no` = 300601 WHERE `id` = 25;
UPDATE `t_permission` SET  `i18n_no` = 300602 WHERE `id` = 26;
UPDATE `t_permission` SET  `i18n_no` = 300603 WHERE `id` = 27;
UPDATE `t_permission` SET  `i18n_no` = 300604 WHERE `id` = 28;
UPDATE `t_permission` SET  `i18n_no` = 300605 WHERE `id` = 29;
UPDATE `t_permission` SET  `i18n_no` = 300800 WHERE `id` = 33;
UPDATE `t_permission` SET  `i18n_no` = 300801 WHERE `id` = 34;
UPDATE `t_permission` SET  `i18n_no` = 300802 WHERE `id` = 35;
UPDATE `t_permission` SET  `i18n_no` = 300803 WHERE `id` = 37;
UPDATE `t_permission` SET  `i18n_no` = 300507 WHERE `id` = 62;
UPDATE `t_permission` SET  `i18n_no` = 300804 WHERE `id` = 63;
UPDATE `t_permission` SET  `i18n_no` = 300508 WHERE `id` = 64;


CREATE TABLE `t_user_group_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_group_id` int DEFAULT NULL,
  `permission` blob,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_user` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_group_id` (`user_group_id`)
);


INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`,
                                       `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`,
                                       `sorting`, `i18n_no`)
VALUES (65, '导出', 1, '/sp-engine/export', 2005, NULL, NULL, 1, '2023-09-28 14:14:48', 0, 1, 4, 0, 0, NULL, '300509');
INSERT INTO `t_role_permission`(`role_id`, `permission_id`, `create_time`, `create_user`, `deleted`,
                                            `tenant_id`)
VALUES (1, 65, '2023-09-28 14:20:31', 1, 0, 1);
INSERT INTO `t_role_permission`(`role_id`, `permission_id`, `create_time`, `create_user`, `deleted`,
                                            `tenant_id`)
VALUES (6, 65, '2023-09-28 14:20:31', 1, 0, 1);

create table t_company_property
(
    id          int auto_increment primary key,
    tenant_id   int                                null,
    property_key        varchar(128)                       null,
    property_value       varchar(500)                       null,
    deleted     tinyint  default 0                 null,
    create_time datetime default CURRENT_TIMESTAMP null,
    update_time datetime                           null on update CURRENT_TIMESTAMP
);

alter table t_process_manage
    add column responsible_person_ids varchar(500) null after description;

INSERT INTO `t_permission`(`id`, `name`, `level`, `tag`, `index_tag`, `description`, `url`, `create_user`, `create_time`, `deleted`, `tenant_id`, `parent_id`, `has_children`, `hidden`, `sorting`, `i18n_no`) VALUES (66, '发布模板', 1, '/sp-engine/saveNewSheetTemplate', 2006, NULL, NULL, 1, '2023-09-28 14:13:41', 0, 1, 4, 0, 0, null,'300510');
INSERT INTO `t_role_permission`( `role_id`, `permission_id`, `create_time`, `create_user`, `deleted`, `tenant_id`) VALUES ( 1, 66, '2023-09-28 14:20:31', 1, 0, 1);

alter table t_group
    add ai_enabled tinyint default 1 null;

ALTER TABLE t_admin   ADD COLUMN is_lock BOOLEAN NOT NULL DEFAULT FALSE ;
ALTER TABLE t_admin   ADD COLUMN login_fail_num INT  NULL DEFAULT 0 ;
ALTER TABLE t_admin   ADD COLUMN last_change_pwd_date datetime   default CURRENT_TIMESTAMP  NULL ;
