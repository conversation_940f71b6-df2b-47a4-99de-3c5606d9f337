DROP TABLE IF EXISTS `t_node_info`;
CREATE TABLE `t_node_info` (
                                        `id` varchar(255) NOT NULL COMMENT 'id',
                                        `ip` varchar(255) DEFAULT NULL COMMENT '注册ip',
                                        `port` varchar(255) DEFAULT NULL COMMENT '注册端口',
                                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '注册的时间',
                                        `user_role` varchar(255) DEFAULT NULL COMMENT '用户角色',
                                        PRIMARY KEY (`id`)
) ;

DROP TABLE IF EXISTS `cloud_file_info`;
CREATE TABLE `cloud_file_info`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `file_name`   varchar(64)  NULL DEFAULT NULL COMMENT '文件名称',
    `file_url`    varchar(512) NULL DEFAULT NULL COMMENT '云文件地址',
    `file_type`   varchar(32)  NULL DEFAULT NULL COMMENT '文件类型',
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    `file_code`   mediumtext   NULL COMMENT '文件信息',
    `sheet_id`    int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARACTER SET = utf8;

-- ----------------------------
-- Table structure for t_action
-- ----------------------------
DROP TABLE IF EXISTS `t_action`;
CREATE TABLE `t_action`
(
    `id`                 int(11)      NOT NULL AUTO_INCREMENT,
    `name`               varchar(64)  NULL DEFAULT NULL COMMENT '执行动作名称',
    `action_code`        int(11)      NULL DEFAULT NULL COMMENT '动作编号',
    `action_flow_id`     int(11)      NULL DEFAULT NULL COMMENT '动作流Id',
    `parent_action_code` int(11)      NULL DEFAULT NULL COMMENT '父执行动作Id',
    `active_type`        int(2)       NULL DEFAULT NULL COMMENT '动作类型',
    `action_param`       text         NULL COMMENT '动作参数',
    `action_result`      text         NULL COMMENT '动作结果',
    `start_flag`         tinyint(4)   NULL DEFAULT 0 COMMENT '起始标识：0：否，1：是',
    `filter_flag`        int(2)       NULL DEFAULT 1 COMMENT '该节点是否需要过滤： 0：需要，1：不需要',
    `filter_content`     varchar(512) NULL DEFAULT NULL COMMENT '过滤文本',
    `create_user`        int(11)      NULL DEFAULT NULL COMMENT '创建人Id',
    `deleted`            tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`        datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`        datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`          int(11)      NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '执行动作'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_action_connect
-- ----------------------------
DROP TABLE IF EXISTS `t_action_connect`;
CREATE TABLE `t_action_connect`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `action_flow_id` int(11)      NULL DEFAULT NULL COMMENT '动作流Id',
    `connect_type`   int(11)      NULL DEFAULT NULL COMMENT '连接类型，',
    `info`           text         NULL COMMENT '连接信息',
    `name`           varchar(256) NULL DEFAULT NULL COMMENT '连接名称',
    `create_user`    int(11)      NULL DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `deleted`        tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `update_time`    datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`      int(11)      NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '执行动作连接信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_action_flow
-- ----------------------------
DROP TABLE IF EXISTS `t_action_flow`;
CREATE TABLE `t_action_flow`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT,
    `name`              varchar(64) NULL DEFAULT NULL COMMENT '执行动作名称',
    `action_code`       int(11)     NULL DEFAULT 1 COMMENT '当前最新的动作编号',
    `run_code`          int(11)     NULL DEFAULT 0 COMMENT '运行编号',
    `source_type`       int(11)     NULL DEFAULT 1 COMMENT '数据来源：1 ：正常生成，2：snapshot生成',
    `create_user`       int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `root_action_param` text        NULL COMMENT '根动作参数-模版',
    `node_info`         text        NULL COMMENT '前端Node节点信息',
    `topic_id`          int(11)     NULL DEFAULT NULL COMMENT 'topicId',
    `deleted`           tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`       datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`       datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`         int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '动作流程'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_action_log
-- ----------------------------
DROP TABLE IF EXISTS `t_action_log`;
CREATE TABLE `t_action_log`
(
    `id`               int(11)     NOT NULL AUTO_INCREMENT,
    `action_id`        int(11)     NULL DEFAULT NULL COMMENT '动作Id',
    `action_code`      int(11)     NULL DEFAULT NULL COMMENT '动作编号',
    `run_code`         int(11)     NULL DEFAULT -1 COMMENT '运行编码',
    `action_flow_id`   int(11)     NULL DEFAULT NULL COMMENT '动作流Id',
    `action_name`      varchar(32) NULL DEFAULT NULL COMMENT '节点名称',
    `active_type`      int(11)     NULL DEFAULT 0 COMMENT '动作类型：当值为-1的时候为动作流日志',
    `action_param`     text        NULL COMMENT '动作参数',
    `action_result`    text        NULL COMMENT '动作结果',
    `action_flow_flag` int(11)     NULL DEFAULT 1 COMMENT '日志是否为动作流日志：0：是，1：不是',
    `single_node_flag` int(11)     NULL DEFAULT 0 COMMENT '单节点运行标识： 0：否 1：是',
    `success_flag`     int(11)     NULL DEFAULT -1 COMMENT '成功标识：-1：未知，0：成功，1：失败',
    `state`            varchar(64) NULL DEFAULT NULL COMMENT '本次执行状态',
    `source_type`      int(11)     NULL DEFAULT 1 COMMENT '数据来源：1 ：正常生成，2：snapshot生成',
    `node_info`        text        NULL COMMENT '前端节点信息',
    `deleted`          tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_user`      int(11)     NULL DEFAULT -1 COMMENT '修改人',
    `create_time`      datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`      datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`        int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '执行动作日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_action_modify_log
-- ----------------------------
DROP TABLE IF EXISTS `t_action_modify_log`;
CREATE TABLE `t_action_modify_log`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `action_flow_id` int(11)      NULL DEFAULT NULL COMMENT '动作流Id',
    `modify_type`    int(11)      NULL DEFAULT NULL COMMENT '类型，1：编辑动作信息，2：执行动作',
    `info`           varchar(512) NULL DEFAULT NULL,
    `create_user`    int(11)      NULL DEFAULT NULL COMMENT '修改人',
    `create_time`    datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `tenant_id`      int(11)      NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '执行动作修改日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_admin
-- ----------------------------
DROP TABLE IF EXISTS `t_admin`;
CREATE TABLE `t_admin`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `username`    varchar(255) NULL DEFAULT NULL,
    `password`    varchar(255) NULL DEFAULT NULL,
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `status`      tinyint(2)   NULL DEFAULT 1 COMMENT '1：正常，2：停用',
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_topic
-- ----------------------------
DROP TABLE IF EXISTS `t_business_topic`;
CREATE TABLE `t_business_topic`
(
    `id`                 int(11)      NOT NULL AUTO_INCREMENT,
    `name`               varchar(255) NULL DEFAULT NULL,
    `type`               int(4)       NULL DEFAULT NULL COMMENT 'BusinessTopicTypeEnum',
    `level`              tinyint(4)   NULL DEFAULT NULL COMMENT '层级，目前只有2级',
    `parent_id`          int(11)      NULL DEFAULT 0 COMMENT '父ID',
    `snapshot_flag`      int(11)      NULL DEFAULT 0 COMMENT '快照标识：0：非快照，1：快照',
    `snapshot_parent_id` int(11)      NULL DEFAULT 0 COMMENT '快照父ID',
    `deleted`            int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`        datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`        datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`          int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_topic_data
-- ----------------------------
DROP TABLE IF EXISTS `t_business_topic_data`;
CREATE TABLE `t_business_topic_data`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT,
    `topic_id`         int(11)      NULL DEFAULT NULL COMMENT '业务主题ID',
    `model_id`         int(11)      NULL DEFAULT NULL COMMENT '数据模型ID或者知识模型ID',
    `data_source_type` varchar(255) NULL DEFAULT NULL COMMENT '数据源类型（1：data_model，2：knowledge_model）',
    `deleted`          int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`      datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`      datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`        int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `topic_id` (`topic_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_topic_kpi
-- ----------------------------
DROP TABLE IF EXISTS `t_business_topic_kpi`;
CREATE TABLE `t_business_topic_kpi`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)     NULL DEFAULT NULL COMMENT '此topic为业务知识模型，下关联的kpi',
    `kpi_id`      int(11)     NULL DEFAULT NULL,
    `deleted`     int(11)     NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_cause_kpi
-- ----------------------------
DROP TABLE IF EXISTS `t_cause_kpi`;
CREATE TABLE `t_cause_kpi`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `cause_id`    int(11)     NULL DEFAULT NULL COMMENT '根因ID',
    `kpi_id`      int(11)     NULL DEFAULT NULL COMMENT '所选kpiId',
    `deleted`     tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `cause_id` (`cause_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_cause_related_variables
-- ----------------------------
DROP TABLE IF EXISTS `t_cause_related_variables`;
CREATE TABLE `t_cause_related_variables`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `cause_id`    int(11)      NULL DEFAULT NULL COMMENT '根因ID',
    `variable`    varchar(255) NULL DEFAULT NULL COMMENT '相关变量',
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `file_id`     int(11)      NULL DEFAULT NULL,
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_connector
-- ----------------------------
DROP TABLE IF EXISTS `t_data_connector`;
CREATE TABLE `t_data_connector`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `pool_id`     int(11)      NULL DEFAULT NULL COMMENT '数据池ID',
    `name`        varchar(255) NULL DEFAULT NULL COMMENT '连接器名称',
    `type`        varchar(255) NULL DEFAULT NULL COMMENT '1:jdbc,2:kafks',
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `status`      tinyint(2)   NULL DEFAULT 0 COMMENT '0：不可用，1：可用',
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_connector_jdbc
-- ----------------------------
DROP TABLE IF EXISTS `t_data_connector_jdbc`;
CREATE TABLE `t_data_connector_jdbc`
(
    `id`           int(11)       NOT NULL AUTO_INCREMENT,
    `connector_id` int(11)       NULL DEFAULT NULL,
    `username`     varchar(1000) NULL DEFAULT NULL COMMENT 'jdbc用户名',
    `password`     varchar(1000) NULL DEFAULT NULL COMMENT 'jdbc密码',
    `connected`    tinyint(2)    NULL DEFAULT NULL COMMENT '连通性（0：否，1：好）',
    `deleted`      tinyint(4)    NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `ip`           varchar(255)  NULL DEFAULT NULL,
    `port`         int(11)       NULL DEFAULT NULL,
    `dbname`       varchar(255)  NULL DEFAULT NULL,
    `tenant_id`    int(11)       NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_connector_kafka
-- ----------------------------
DROP TABLE IF EXISTS `t_data_connector_kafka`;
CREATE TABLE `t_data_connector_kafka`
(
    `id`           int(11)       NOT NULL AUTO_INCREMENT,
    `connector_id` int(11)       NULL DEFAULT NULL,
    `borker_list`  varchar(1000) NULL DEFAULT NULL,
    `topic_name`   varchar(255)  NULL DEFAULT NULL,
    `group_id`     varchar(255)  NULL DEFAULT NULL,
    `offset_reset` varchar(255)  NULL DEFAULT '2' COMMENT '1：earliest，2：lastest，3：none',
    `deleted`      tinyint(4)    NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`    int(11)       NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_extractor
-- ----------------------------
DROP TABLE IF EXISTS `t_data_extractor`;
CREATE TABLE `t_data_extractor`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT,
    `connector_id` int(11)      NULL DEFAULT NULL COMMENT '数据链接ID',
    `name`         varchar(255) NULL DEFAULT NULL COMMENT '数据提取名称',
    `type`         varchar(255) NULL DEFAULT '0' COMMENT '类型（0：jdbc，1：kafka）',
    `table_name`   varchar(255) NULL DEFAULT NULL COMMENT '选择的表名',
    `file_id`      int(11)      NULL DEFAULT NULL,
    `status`       tinyint(255) NULL DEFAULT 0 COMMENT '0：不可用（只创建了提取器，没有选择字段），1：可用（提交了字段）',
    `deleted`      tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `start`        tinyint(2)   NULL DEFAULT 1 COMMENT '0：暂停，1：开始',
    `tenant_id`    int(11)      NULL DEFAULT NULL,
    `where_rule`   varchar(500) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model`;
CREATE TABLE `t_data_model`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `pool_id`     int(11)      NULL DEFAULT NULL COMMENT '所属数据池ID',
    `name`        varchar(255) NULL DEFAULT NULL COMMENT '数据模型名称',
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `status`      tinyint(2)   NULL DEFAULT NULL COMMENT '0:不可用，1：可用',
    `caseid`      int(11)      NULL DEFAULT NULL,
    `event`       int(11)      NULL DEFAULT NULL,
    `time`        int(11)      NULL DEFAULT NULL,
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `pool_id` (`pool_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_file
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_file`;
CREATE TABLE `t_data_model_file`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT,
    `data_model_id`    int(11)      NULL DEFAULT NULL,
    `file_id`          int(11)      NULL DEFAULT NULL,
    `deleted`          tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`      datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`      datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `active`           tinyint(2)   NULL DEFAULT 0 COMMENT '（0：次表，1：主表）',
    `file_name`        varchar(255) NULL DEFAULT NULL COMMENT '文件名称（这个字段冗余一下，1.1版本kpi查询会方便一些）',
    `tenant_id`        int(11)      NULL DEFAULT NULL,
    `spark_table_name` varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `data_model_id` (`data_model_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_file_load_log
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_file_load_log`;
CREATE TABLE `t_data_model_file_load_log`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `log_id`      int(11)     NULL DEFAULT NULL,
    `file_id`     int(11)     NULL DEFAULT NULL,
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `deleted`     tinyint(2)  NULL DEFAULT 0,
    `status`      tinyint(2)  NULL DEFAULT NULL COMMENT '0:未加载，1：加载中，2：加载成功，3：加载失败',
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_file_master
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_file_master`;
CREATE TABLE `t_data_model_file_master`
(
    `id`                  int(11)      NOT NULL AUTO_INCREMENT,
    `data_model_id`       int(11)      NULL DEFAULT NULL,
    `origin_file_id`      int(11)      NULL DEFAULT NULL COMMENT '源主表ID',
    `merge_file_id`       int(11)      NULL DEFAULT NULL COMMENT '合并表之后的表ID',
    `status`              tinyint(2)   NULL DEFAULT 0 COMMENT '0：未加载，1：加载中，2：加载成功',
    `deleted`             tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`         datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`         datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `merge_file_uniqname` varchar(255) NULL DEFAULT NULL COMMENT 'merge后的文件UUID名称',
    `merge_sql`           text         NULL COMMENT '合并的sql',
    `case_merge_sql`      text         NULL,
    `tenant_id`           int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_foreign
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_foreign`;
CREATE TABLE `t_data_model_foreign`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `data_model_id`  int(11)      NULL DEFAULT NULL,
    `file_id`        int(11)      NULL DEFAULT NULL,
    `field_id`       int(11)      NULL DEFAULT NULL,
    `field`          varchar(255) NULL DEFAULT NULL,
    `point_file_id`  int(11)      NULL DEFAULT NULL,
    `point_field_id` int(11)      NULL DEFAULT NULL,
    `point_field`    varchar(255) NULL DEFAULT NULL,
    `deleted`        tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`    datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`    datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `has_merge`      tinyint(2)   NULL DEFAULT 0 COMMENT '0：未merge，1：已merge',
    `tenant_id`      int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_load_log
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_load_log`;
CREATE TABLE `t_data_model_load_log`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT,
    `start_time`    datetime(0)  NULL DEFAULT NULL,
    `end_time`      datetime(0)  NULL DEFAULT NULL,
    `data_model_id` int(11)      NULL DEFAULT NULL,
    `tenant_id`     int(11)      NULL DEFAULT NULL,
    `deleted`       tinyint(255) NULL DEFAULT 0,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_load_msg
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_load_msg`;
CREATE TABLE `t_data_model_load_msg`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `log_id`      int(11)     NULL DEFAULT NULL,
    `msg`         text        NULL,
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_model_site
-- ----------------------------
DROP TABLE IF EXISTS `t_data_model_site`;
CREATE TABLE `t_data_model_site`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT,
    `file_id`       int(11)     NULL DEFAULT NULL COMMENT '文件Id',
    `data_model_id` int(11)     NULL DEFAULT NULL COMMENT '数据模型Id',
    `top_ratio`     varchar(64) NULL DEFAULT NULL,
    `left_ratio`    varchar(64) NULL DEFAULT NULL,
    `top_value`     varchar(64) NULL DEFAULT NULL,
    `left_value`    varchar(64) NULL DEFAULT NULL,
    `deleted`       tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`   datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`   datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`     int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '数据模型位置信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_pool
-- ----------------------------
DROP TABLE IF EXISTS `t_data_pool`;
CREATE TABLE `t_data_pool`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `pool_name`   varchar(255) NULL DEFAULT NULL,
    `create_time` datetime(0)  NULL DEFAULT NULL,
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_pool_file
-- ----------------------------
DROP TABLE IF EXISTS `t_data_pool_file`;
CREATE TABLE `t_data_pool_file`
(
    `id`           int(11)     NOT NULL AUTO_INCREMENT,
    `data_pool_id` int(11)     NULL DEFAULT NULL,
    `file_id`      int(11)     NULL DEFAULT NULL,
    `deleted`      tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `file_type`    tinyint(2)  NULL DEFAULT NULL,
    `tenant_id`    int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `data_pool_id` (`data_pool_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_task
-- ----------------------------
DROP TABLE IF EXISTS `t_data_task`;
CREATE TABLE `t_data_task`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT,
    `pool_id`      int(11)      NULL DEFAULT NULL,
    `name`         varchar(255) NULL DEFAULT NULL,
    `connector_id` int(11)      NULL DEFAULT NULL,
    `rate_type`    tinyint(2)   NULL DEFAULT NULL COMMENT '1：每小时，2：每天，3：每周',
    `deleted`      tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `minute`       int(2)       NULL DEFAULT NULL COMMENT '分钟',
    `hour`         int(2)       NULL DEFAULT NULL COMMENT '小时',
    `week`         int(2)       NULL DEFAULT NULL COMMENT '周一-周日',
    `tenant_id`    int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_task_extractor
-- ----------------------------
DROP TABLE IF EXISTS `t_data_task_extractor`;
CREATE TABLE `t_data_task_extractor`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT,
    `task_id`      int(11)      NULL DEFAULT NULL COMMENT 't_data_task表的主键ID',
    `file_id`      int(11)      NULL DEFAULT NULL COMMENT 't_file表的主键ID',
    `deleted`      tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`  datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`  datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `extractor_id` int(11)      NULL DEFAULT NULL COMMENT '该数据任务选择的提取器ID',
    `task_name`    varchar(255) NULL DEFAULT NULL COMMENT '就是在数仓创建的链接器名称，用来向数仓查询状态用的',
    `tenant_id`    int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_data_task_log
-- ----------------------------
DROP TABLE IF EXISTS `t_data_task_log`;
CREATE TABLE `t_data_task_log`
(
    `id`         int(11)     NOT NULL AUTO_INCREMENT,
    `start_time` datetime(0) NULL DEFAULT NULL,
    `end_time`   datetime(0) NULL DEFAULT NULL,
    `task_id`    int(11)     NULL DEFAULT NULL,
    `pool_id`    int(11)     NULL DEFAULT NULL,
    `status`     tinyint(2)  NULL DEFAULT 2 COMMENT '1：进行中,2：成功，3：失败',
    `tenant_id`  int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_event_start_end
-- ----------------------------
DROP TABLE IF EXISTS `t_event_start_end`;
CREATE TABLE `t_event_start_end`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)      NULL DEFAULT NULL,
    `file_id`     int(11)      NULL DEFAULT NULL,
    `start`       varchar(255) NULL DEFAULT NULL COMMENT '开始事件',
    `end`         varchar(255) NULL DEFAULT NULL COMMENT '结束事件',
    `create_time` datetime(0)  NULL DEFAULT NULL,
    `deleted`     tinyint(2)   NULL DEFAULT 0,
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARACTER SET = utf8;

-- ----------------------------
-- Table structure for t_file
-- ----------------------------
DROP TABLE IF EXISTS `t_file`;
CREATE TABLE `t_file`
(
    `id`              int(11)             NOT NULL AUTO_INCREMENT,
    `caseId`          int(11)             NULL     DEFAULT NULL COMMENT '所设caseId列',
    `event`           int(11)             NULL     DEFAULT NULL COMMENT '所设event列',
    `time`            int(11)             NULL     DEFAULT NULL COMMENT '所设time列',
    `upload_status`   tinyint(4)          NULL     DEFAULT 0 COMMENT '文件上传是否成功（0：否，1：是）',
    `load_start_time` datetime(0)         NULL     DEFAULT NULL COMMENT '落盘数据开始时间',
    `load_end_time`   datetime(0)         NULL     DEFAULT NULL COMMENT '落盘数据结束时间',
    `status`          tinyint(4)          NULL     DEFAULT 0 COMMENT '数据加载状态（0：未成功，1：已成功）',
    `attributes`      text                NULL COMMENT '表中除三要素字段外的其他字段，以,连接',
    `deleted`         tinyint(4)          NULL     DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `filename`        varchar(255)        NULL     DEFAULT NULL,
    `uniq_filename`   varchar(255)        NULL     DEFAULT NULL,
    `create_time`     datetime(0)         NULL     DEFAULT CURRENT_TIMESTAMP(0),
    `type`            tinyint(4)          NULL     DEFAULT NULL COMMENT '文件类型',
    `update_time`     datetime(0)         NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `total_case`      int(11)             NULL     DEFAULT NULL COMMENT '总的case数量',
    `connector_id`    int(11)             NULL     DEFAULT NULL,
    `parent_id`       int(11)             NULL     DEFAULT NULL COMMENT '文件的源ID（选了主表之后会重新生成一条表数据）',
    `tenant_id`       int(11)             NULL     DEFAULT NULL,
    `char_encode`     varchar(255)        NULL     DEFAULT NULL,
    `separator_mark`  varchar(255)        NULL     DEFAULT NULL,
    `length`          bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'spark占用空间大小',
    `original_length` bigint(40)          NULL     DEFAULT 0 COMMENT '原始文件占用空间大小',
    `error_message` varchar(5000)         NULL ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_file_children
-- ----------------------------
DROP TABLE IF EXISTS `t_file_children`;
CREATE TABLE `t_file_children`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `file_id`     int(11)     NULL DEFAULT NULL COMMENT '新的聚合宽表ID',
    `children_id` int(11)     NULL DEFAULT NULL COMMENT '小表ID，多个小表ID聚合成一个大表（以供查询kpi）',
    `deleted`     tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `topic_id`    int(11)     NULL DEFAULT NULL,
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_file_field
-- ----------------------------
DROP TABLE IF EXISTS `t_file_field`;
CREATE TABLE `t_file_field`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT,
    `file_id`          int(11)      NULL DEFAULT NULL,
    `field`            varchar(255) NULL DEFAULT NULL,
    `field_type`       tinyint(2)   NULL DEFAULT NULL COMMENT 'DataTypeEnum',
    `deleted`          tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`      datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`      datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `alias`            varchar(255) NULL DEFAULT NULL COMMENT '别名，字段可能会被改名',
    `field_origin`     varchar(255) NULL DEFAULT NULL,
    `has_prikey`       tinyint(2)   NULL DEFAULT 0 COMMENT '是否是主键（0：否，1：是）',
    `has_update_field` tinyint(2)   NULL DEFAULT NULL,
    `tenant_id`        int(11)      NULL DEFAULT NULL,
    `date_format`      varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_file_variant
-- ----------------------------
DROP TABLE IF EXISTS `t_file_variant`;
CREATE TABLE `t_file_variant`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT,
    `file_id`       int(11)     NULL DEFAULT NULL COMMENT '文件ID',
    `variant`       text CHARACTER SET utf8NULL COMMENT '变体值',
    `count`         int(11)     NULL DEFAULT NULL COMMENT '当前变体数量',
    `create_time`   datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `deleted`       tinyint(2)  NULL DEFAULT 0 COMMENT '是否删除（0：未，1：已删除）',
    `type`          tinyint(2)  NULL DEFAULT 0 COMMENT '0:file相关的最大变体，1：topic相关的可变变体（过滤项）',
    `variant_id`    int(11)     NULL DEFAULT NULL COMMENT '变体ID（1、2、3。。。）',
    `topic_id`      int(11)     NULL DEFAULT NULL,
    `data_model_id` int(11)     NULL DEFAULT NULL,
    `tenant_id`     int(11)     NULL DEFAULT NULL,
    `skip`          tinyint(2)  NULL DEFAULT 0 COMMENT '0：不是跳过的，1：是跳过的',
    PRIMARY KEY (`id`),
    INDEX `file_id` (`file_id`, `topic_id`, `type`),
    INDEX `count` (`count`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_file_variant_id
-- ----------------------------
DROP TABLE IF EXISTS `t_file_variant_id`;
CREATE TABLE `t_file_variant_id`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `temp`      int(11) NULL DEFAULT NULL,
    `tenant_id` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi`;
CREATE TABLE `t_kpi`
(
    `id`              int(11)        NOT NULL AUTO_INCREMENT,
    `name`            varchar(255)   NULL DEFAULT NULL COMMENT 'kpi名称',
    `unit`            varchar(255)   NULL DEFAULT NULL COMMENT 'kpi单位，是个字符串',
    `type`            tinyint(4)     NULL DEFAULT NULL COMMENT '1:越大越优，2：越小越优',
    `base_line`       varchar(255)   NULL DEFAULT NULL COMMENT '基线值',
    `expression`      varchar(10000)   NULL DEFAULT NULL COMMENT '公式',
    `deleted`         tinyint(4)     NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`     datetime(0)    NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`     datetime(0)    NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `target`          decimal(13, 2) NULL DEFAULT NULL,
    `tenant_id`       int(11)        NULL DEFAULT NULL,
    `group_column_id` int(11)        NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_comment
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_comment`;
CREATE TABLE `t_kpi_comment`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_relation_id` int(11)      NULL DEFAULT NULL COMMENT 'KPI执行id',
    `comment`         varchar(500) NULL DEFAULT NULL COMMENT '评论内容',
    `create_id`       int(11)      NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`     datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`         int(11)      NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`        int(11)      NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`       int(11)      NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi评论 kpi评论表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_log
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_log`;
CREATE TABLE `t_kpi_log`
(
    `id`                   int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_relation_id`      int(11)     NULL DEFAULT NULL COMMENT 'KPI执行id',
    `type`                 int(11)     NULL DEFAULT NULL COMMENT '0任务分配/1状态/2:完成任务/3重启任务/4编辑任务/5删除任务/6创建任务',
    `distribution_user_id` int(11)     NULL DEFAULT NULL COMMENT '操作人的用户id',
    `assigned_user_id`     int(11)     NULL DEFAULT NULL COMMENT '被分配的用户id',
    `create_id`            int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`          datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`              int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`             int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`            int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi日志 kpi日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_relation
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_relation`;
CREATE TABLE `t_kpi_relation`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_id`        int(11)     NULL DEFAULT NULL COMMENT 'kpi主键',
    `data_model_id` int(11)     NULL DEFAULT NULL COMMENT '数据模型id',
    `column_id`     int(11)     NULL DEFAULT NULL COMMENT '列id',
    `create_id`     int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`   datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`       int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`      int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`     int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi关联表 kpi关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_report_forms
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_report_forms`;
CREATE TABLE `t_kpi_report_forms`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `report_forms_name` varchar(50) NULL DEFAULT NULL COMMENT '报表名称',
    `date_type`         int(11)     NULL DEFAULT NULL COMMENT '报表时间类型（0：每日；1：每周；2：每月；3：工作日）',
    `date`              varchar(50) NULL DEFAULT NULL COMMENT '日期（周/月）',
    `time`              varchar(50) NULL DEFAULT NULL COMMENT '报表时间',
    `status`            int(11)     NULL DEFAULT NULL COMMENT '状态（0：暂停；1：运行中）',
    `create_id`         int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`       datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`           int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`          int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`         int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi报表 kpi报表表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_report_forms_kpis
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_report_forms_kpis`;
CREATE TABLE `t_kpi_report_forms_kpis`
(
    `id`                  int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_report_forms_id` int(11)     NULL DEFAULT NULL COMMENT 'kpi报表id',
    `kpi_id`              int(11)     NULL DEFAULT NULL COMMENT 'kpi的主键',
    `create_id`           int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`         datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`             int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`            int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`           int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi报表关联的kpi的ids'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_report_forms_users
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_report_forms_users`;
CREATE TABLE `t_kpi_report_forms_users`
(
    `id`                  int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_report_forms_id` int(11)     NULL DEFAULT NULL COMMENT 'kpi报表id',
    `user_id`             int(11)     NULL DEFAULT NULL COMMENT '发送人的用户id',
    `create_id`           int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`         datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`             int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`            int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`           int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi报表关联的用户'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_task
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_task`;
CREATE TABLE `t_kpi_task`
(
    `id`              int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_relation_id` int(11)     NULL DEFAULT NULL COMMENT 'KPI执行id',
    `user_id`         int(11)     NULL DEFAULT NULL COMMENT '责任人id',
    `task_name`       varchar(50) NULL DEFAULT NULL COMMENT '任务名称',
    `status`          int(11)     NULL DEFAULT NULL COMMENT '状态（0未完成；1已完成；2重启任务）',
    `create_id`       int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`     datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`         int(11)     NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`        int(11)     NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`       int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi任务 kpi任务表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_kpi_value
-- ----------------------------
DROP TABLE IF EXISTS `t_kpi_value`;
CREATE TABLE `t_kpi_value`
(
    `id`                  int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `kpi_report_forms_id` int(11)      NULL DEFAULT NULL COMMENT '报表id',
    `kpi_relation_id`     bigint(20)   NULL DEFAULT NULL COMMENT 'kpi执行id',
    `kpi_id`              int(11)      NULL DEFAULT NULL COMMENT 'kpi主键',
    `kpi_value`           varchar(500) NULL DEFAULT NULL COMMENT 'kpi的值',
    `kpi_time`            varchar(50)  NULL DEFAULT NULL COMMENT 'kpi的时间',
    `type`                int(11)      NULL DEFAULT NULL COMMENT '时间类型（0：日；1：周；2：月；3：工作日）',
    `create_id`           int(11)      NULL DEFAULT NULL COMMENT '创建人Id',
    `create_time`         datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `deleted`             int(11)      NULL DEFAULT NULL COMMENT '是否逻辑删除',
    `order_no`            int(11)      NULL DEFAULT NULL COMMENT '排序号',
    `tenant_id`           int(11)      NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'kpi的值 kpi在各时间段的值'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_mq_log
-- ----------------------------
DROP TABLE IF EXISTS `t_mq_log`;
CREATE TABLE `t_mq_log`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT,
    `type`          int(11)     NULL DEFAULT NULL COMMENT '消息类型',
    `param`         text        NULL COMMENT '消息入参',
    `run_state`     int(11)     NULL DEFAULT 0 COMMENT '运行状态：0：开始执行，1：执行成功，2：执行失败',
    `error_message` text        NULL COMMENT '错误信息',
    `create_time`   datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`   datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`     int(11)     NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '消息队列日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_parquet_partition
-- ----------------------------
DROP TABLE IF EXISTS `t_parquet_partition`;
CREATE TABLE `t_parquet_partition`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `file_id`        int(11)      NULL DEFAULT NULL,
    `partition_path` varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_root_cause
-- ----------------------------
DROP TABLE IF EXISTS `t_root_cause`;
CREATE TABLE `t_root_cause`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `cause_name`  varchar(255) NULL DEFAULT NULL COMMENT '根因分析名称',
    `sort_rule`   int(11)      NULL DEFAULT NULL COMMENT '排序规则（其实对应的是kpiId）',
    `deleted`     tinyint(4)   NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_task_table_log
-- ----------------------------
DROP TABLE IF EXISTS `t_task_table_log`;
CREATE TABLE `t_task_table_log`
(
    `id`          int(11)       NOT NULL AUTO_INCREMENT,
    `table_id`    int(11)       NULL DEFAULT NULL,
    `start_time`  datetime(0)   NULL DEFAULT NULL,
    `end_time`    datetime(0)   NULL DEFAULT NULL,
    `status`      varchar(255)  NULL DEFAULT NULL COMMENT '状态（0：未开始，1：运行中，2：完成，3：失败）',
    `extract_num` int(11)       NULL DEFAULT NULL COMMENT '提取数量',
    `deleted`     tinyint(4)    NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '开始时间',
    `update_time` datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '结束时间',
    `task_log_id` int(11)       NULL DEFAULT NULL,
    `fail_desc`   varchar(1000) NULL DEFAULT NULL COMMENT '失败原因',
    `tenant_id`   int(11)       NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_filter
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_filter`;
CREATE TABLE `t_topic_filter`
(
    `id`          int(11)       NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)       NULL DEFAULT NULL,
    `sheet_id`    int(11)       NULL DEFAULT NULL,
    `type`        int(4)        NULL DEFAULT NULL,
    `param`       text          NULL,
    `value`       text          NULL,
    `deleted`     tinyint(2)    NULL DEFAULT 0,
    `create_time` datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `file_id`     int(11)       NULL DEFAULT NULL,
    `field_id`    int(11)       NULL DEFAULT NULL,
    `operation`   tinyint(2)    NULL DEFAULT NULL,
    `prikey_id`   int(255)      NULL DEFAULT NULL COMMENT '这一批过滤项的ID，删除也依靠这个ID',
    `tenant_id`   int(11)       NULL DEFAULT NULL,
    `biz_type`    tinyint(2)    NULL DEFAULT 0 COMMENT '0：添加过滤项，1：创建topic初始化过滤项 20:对比左，21对比右',
    `param2`      varchar(1000) NULL DEFAULT NULL,
    `value2`      varchar(255)  NULL DEFAULT NULL,
    `unit`        tinyint(2)    NULL DEFAULT NULL COMMENT '1：秒，2：分，3：时，4：天',
    `param_type`  int(4)        NULL DEFAULT NULL COMMENT '(type=900时，10：第一次发生，20：最后一次发生),(type=800时，10：直接跟随，20：跟随，30：不直接跟随，40：不跟随)',
    `param_type2` int(255)      NULL DEFAULT NULL COMMENT 'type=900时，10：第一次发生，20：最后一次发生',
    `time_unit`   varchar(32)   NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_filter_caseid
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_filter_caseid`;
CREATE TABLE `t_topic_filter_caseid`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)     NULL DEFAULT NULL,
    `deleted`     tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_filter_prikey
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_filter_prikey`;
CREATE TABLE `t_topic_filter_prikey`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `temp`      int(11) NULL DEFAULT NULL,
    `tenant_id` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_sheet
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet`;
CREATE TABLE `t_topic_sheet`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)      NULL DEFAULT NULL COMMENT '主题ID，这里主要是业务分析图谱',
    `type`        tinyint(4)   NULL DEFAULT NULL COMMENT 'sheet类型（1：流程AI，2：流程变体，3：案例视图，4：业务视图，5：流程视图）',
    `order`       tinyint(4)   NULL DEFAULT NULL COMMENT '排序字段',
    `name`        varchar(255) NULL DEFAULT NULL,
    `deleted`     int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time` datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`   int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

-- ----------------------------
-- Table structure for t_topic_sheet_cause
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet_cause`;
CREATE TABLE `t_topic_sheet_cause`
(
    `id`             int(11)     NOT NULL AUTO_INCREMENT,
    `topic_sheet_id` int(11)     NULL DEFAULT NULL,
    `cause_id`       int(11)     NULL DEFAULT NULL,
    `deleted`        int(11)     NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`    datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`    datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`      int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `topic_sheet_id` (`topic_sheet_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_sheet_conformance
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet_conformance`;
CREATE TABLE `t_topic_sheet_conformance`
(
    `id`              int(11)       NOT NULL AUTO_INCREMENT,
    `topic_sheet_id`  int(11)       NULL DEFAULT NULL COMMENT '表格ID',
    `file_variant_id` int(11)       NULL DEFAULT NULL COMMENT '流程变体表的主键ID',
    `deleted`         tinyint(4)    NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`     datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`     datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `variant_id`      varchar(5000) NULL DEFAULT NULL COMMENT '自定义变体（在数据库中查询以此变体ID来查）',
    `has_default`     tinyint(2)    NULL DEFAULT 0 COMMENT '是否是默认变体（0：否，1：是）',
    `tenant_id`       int(11)       NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_sheet_kpi
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet_kpi`;
CREATE TABLE `t_topic_sheet_kpi`
(
    `id`             int(11)     NOT NULL AUTO_INCREMENT,
    `topic_sheet_id` int(11)     NULL DEFAULT NULL COMMENT '当业务主题为图谱时，则可能会关联kpi',
    `kpi_id`         int(11)     NULL DEFAULT NULL,
    `deleted`        int(11)     NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`    datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`    datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `from_knowledge` tinyint(2)  NULL DEFAULT 0 COMMENT '是否来自于知识模型（0：否，1：是）',
    `tenant_id`      int(11)     NULL DEFAULT NULL,
    `type`           tinyint(2)  NULL DEFAULT 0 COMMENT '0：正常创建，1：初始创建，不能删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_sheet_new
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet_new`;
CREATE TABLE `t_topic_sheet_new`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT,
    `topic_sheet_id`   int(11)       NULL DEFAULT NULL COMMENT '当业务主题为图谱时，则可能会关联kpi',
    `components_value` text CHARACTER SET utf8 COMMENT '组件值',
    `components_info`  varchar(1024) NULL DEFAULT NULL COMMENT '组件信息',
    `deleted`          tinyint(4)    NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`      datetime(0)   NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`      datetime(0)   NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`        int(11)       NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'new sheet信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_sheet_variable
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_sheet_variable`;
CREATE TABLE `t_topic_sheet_variable`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `topic_sheet_id` int(11)      NULL DEFAULT NULL,
    `variable`       varchar(255) NULL DEFAULT NULL,
    `file_id`        int(11)      NULL DEFAULT NULL,
    `deleted`        int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`    datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`    datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `field_id`       int(11)      NULL DEFAULT NULL,
    `tenant_id`      int(11)      NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_topic_snapshot_log
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_snapshot_log`;
CREATE TABLE `t_topic_snapshot_log`
(
    `id`                int(11)     NOT NULL AUTO_INCREMENT,
    `topic_id`          int(11)     NULL DEFAULT NULL COMMENT 'topicId',
    `snapshot_topic_id` int(11)     NULL DEFAULT NULL COMMENT '快照的topicId',
    `create_user`       int(11)     NULL DEFAULT NULL COMMENT '创建人Id',
    `deleted`           tinyint(4)  NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
    `create_time`       datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`       datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
    `tenant_id`         int(11)     NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = 'topic发布记录'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_website_contact
-- ----------------------------
DROP TABLE IF EXISTS `t_website_contact`;
CREATE TABLE `t_website_contact`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `name`        varchar(255) NULL DEFAULT NULL,
    `company`     varchar(255) NULL DEFAULT NULL,
    `phone`       varchar(255) NULL DEFAULT NULL,
    `email`       varchar(255) NULL DEFAULT NULL,
    `position`    varchar(255) NULL DEFAULT NULL,
    `create_time` datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
    `ip_add`      varchar(126) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

SET
    FOREIGN_KEY_CHECKS = 1;

alter table t_file_field
    add expression varchar(255) null;
alter table t_data_model
    add num_buckets int null;
alter table t_data_connector_jdbc
    add pool_id int null;
alter table t_data_extractor
    add spark_table_name varchar(255) null;
alter table t_data_extractor
    add pool_id int null;

alter table t_action_log
    add status tinyint null;
alter table t_action_log
    add task_id int null;
alter table t_action_log
    add user_id int null;
alter table t_action_log
    add type tinyint null;


DROP TABLE IF EXISTS `t_sheet_menu`;
CREATE TABLE `t_sheet_menu`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `user_id`   int(11) NULL DEFAULT NULL,
    `sheet_id`  int(11) NULL DEFAULT NULL,
    `sort`      int(11) NULL DEFAULT NULL,
    `tenant_id` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  ROW_FORMAT = Dynamic;

SET
    FOREIGN_KEY_CHECKS = 1;


DROP TABLE IF EXISTS `t_topic_menu`;
CREATE TABLE `t_topic_menu`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `user_id`   int(11) NULL DEFAULT NULL,
    `topic_id`  int(11) NULL DEFAULT NULL,
    `sort`      int(11) NULL DEFAULT NULL,
    `tenant_id` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  ROW_FORMAT = Dynamic;

SET
    FOREIGN_KEY_CHECKS = 1;

alter TABLE t_sheet_menu
    add COLUMN deleted int(11) DEFAULT 0;


create table `t_simulation`
(
    id          int auto_increment,
    name        varchar(64)                           null comment '执行动作名称',
    file_id     int                                   null comment '文件Id',
    topic_id    int                                   null comment 'topicId',
    deleted     tinyint  default 0                    null comment '0：未删除，1：已删除',
    create_time datetime default CURRENT_TIMESTAMP(0) null,
    update_time datetime                              null on update CURRENT_TIMESTAMP(0),
    tenant_id   int                                   null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;



create table `t_simulation_event`
(
    id                       int auto_increment,
    simulation_id            int                                      null comment '仿真Id',
    name                     varchar(64)                              null comment '事件名称',
    init_gateway_probability varchar(512)                             null comment ' 初始 网关概率信息 Map<eventId,probability></>json{}',
    resource_pool_id         int                                      null comment '资源池Id',
    event_type               int         default 0                    null comment '事件类型，-2：结束 -1：开始， 0:事件 ，1：专属网关（开始），2：并行网关（开始）， 11：专属网关（结束），12：并行网关（结束）',
    element_id               varchar(64)                              null comment 'bpmn事件Id',
    deleted                  tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time              datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time              datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id                int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;

create table `t_simulation_event_attr`
(
    id                  int auto_increment,
    simulation_id       int                                      null comment '仿真Id',
    simulation_event_id int                                      null comment '仿真事件Id',
    programme_id        int                                      null comment '方案Id',
    gateway_probability varchar(512)                             null comment '网关概率信息 Map<eventId,probability></>json{}',
    run_time            int                                      null comment '执行时间',
    run_cost            int                                      null comment '运行成本',
    deleted             tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time         datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time         datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id           int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;

create table `t_simulation_programme`
(
    id            int auto_increment,
    simulation_id int                                      null comment '仿真Id',
    name          varchar(64)                              null comment '方案名称',
    deleted       tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time   datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time   datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id     int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;



create table `t_simulation_resource`
(
    id               int auto_increment,
    resource_pool_id int                                      null comment '资源池Id',
    programme_id     int                                      null comment '方案Id',
    simulation_id    int                                      null comment '仿真Id',
    name             varchar(64)                              null comment '资源名称',
    available_time   varchar(64)                              null comment '资源可用时间',
    resource_cost    int                                      null comment '资源成本',
    number           int         default 0                    null comment '资源数量',
    start_time       varchar(64)                              null comment '开始时间',
    end_time         varchar(64)                              null comment '结束时间',
    deleted          tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time      datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time      datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id        int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;
create table `t_simulation_resource_pool`
(
    id            int auto_increment,
    simulation_id int                                      null comment '仿真Id',
    name          varchar(64)                              null comment '资源名称',
    deleted       tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time   datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time   datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id     int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;

create table `t_simulation_run_frequency`
(
    id            int auto_increment,
    programme_id  int                                      null comment '方案Id',
    simulation_id int                                      null comment '仿真Id',
    name          varchar(64)                              null comment '执行名称',
    start_event   varchar(64)                              null comment '开始事件名称',
    start_time    varchar(64)                              null comment '开始时间',
    end_time      varchar(64)                              null comment '结束时间',
    frequency     varchar(64)                              null comment '频率：每天触发多少个',
    week          varchar(256)                             null comment '周几触发，多个时用@符号分割',
    deleted       tinyint     default 0                    null comment '0：未删除，1：已删除',
    create_time   datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time   datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id     int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;

create table `t_simulation_run_result`
(
    id            int auto_increment,
    programme_id  int                                      null comment '方案Id',
    simulation_id int                                      null comment '仿真Id',
    result        varchar(6553)                            null comment '运行结果',
    create_time   datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time   datetime(0)                              null on update CURRENT_TIMESTAMP(0),
    tenant_id     int                                      null comment '租户ID',
    PRIMARY KEY (`id`)
) CHARSET = utf8;

alter table t_simulation_event_attr
    alter column run_time set default 1;

alter table t_simulation_event_attr
    alter column run_cost set default 0;


ALTER TABLE t_topic_filter
    ADD COLUMN component_id int(11) DEFAULT NULL COMMENT '组件ID';
ALTER TABLE t_topic_filter
    ADD COLUMN expression varchar(1000) CHARACTER SET utf8DEFAULT NULL;
ALTER TABLE t_topic_filter
    ADD COLUMN invert tinyint(11) DEFAULT '0' COMMENT '是否取反(0:否,1:是)';

ALTER TABLE t_data_connector
    ADD COLUMN `biz_type` tinyint(4) DEFAULT NULL COMMENT '1：标准链接，2：自定义链接';
ALTER TABLE t_data_connector
    ADD COLUMN `jdbc_type` tinyint(4) DEFAULT NULL COMMENT '1：mysql,2:oracle,3:db2,4:hive,5:postgreSql,6:impala,7:sqlserver';
ALTER TABLE t_data_connector
    ADD COLUMN `connect_link` varchar(10000) DEFAULT NULL COMMENT '自定义链接';
ALTER TABLE t_data_connector_jdbc
    ADD COLUMN `schema_name` varchar(1000) DEFAULT NULL;


DROP TABLE IF EXISTS `t_sheet_component`;
CREATE TABLE `t_sheet_component`
(
    `id`              int(11)    NOT NULL AUTO_INCREMENT,
    `name`            varchar(255)    DEFAULT NULL,
    `sheet_id`        int(11)    NULL DEFAULT NULL,
    `type`            int(11)    NULL DEFAULT NULL COMMENT '组件类型（10：流程视图，20：变体视图，30：OLAP表单，31：拆线图，32：柱状图，33：饼图，40：KPI，50：文字，51：图片，60：吞吐时间，70：变量过滤，71：时间过滤，80：流程裁剪）',
    `event_field_id`  int(11)    NULL DEFAULT NULL COMMENT '动态选择的事件列，流程视图相关字段',
    `has_aggre_event` tinyint(4) NULL DEFAULT 0 COMMENT '是否聚合相同节点，0：否，1：是，流程视图相关字段',
    `create_time`     datetime        DEFAULT CURRENT_TIMESTAMP,
    `update_time`     datetime        DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted`         tinyint(4) NULL DEFAULT 0,
    `tenant_id`       int(11)    NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARSET = utf8;


CREATE TABLE `t_component_work_time`
(
    `id`           int(11)    NOT NULL AUTO_INCREMENT,
    `component_id` int(11)    NULL DEFAULT NULL,
    `week`         int(11)    NULL DEFAULT NULL COMMENT '周一-周日分别为1-7',
    `start_time`   varchar(50)     DEFAULT NULL,
    `end_time`     varchar(50)     DEFAULT NULL,
    `create_time`  datetime        DEFAULT CURRENT_TIMESTAMP,
    `update_time`  datetime        DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted`      tinyint(4) NULL DEFAULT 0,
    `tenant_id`    int(11)    NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARSET = utf8;


create table t_transformation_attr
(
    id                    int auto_increment
        primary key,
    data_pool_id          int                                      null comment '数据池Id',
    transformation_sql_id int                                      null,
    map_key               varchar(128)                             null comment '映射Key',
    map_value             varchar(128)                             null comment '映射value',
    deleted               int         default 0                    null comment '0：未删除，1：已删除',
    create_time           datetime(0) default CURRENT_TIMESTAMP(0) null,
    update_time           datetime(0)                              null on update CURRENT_TIMESTAMP,
    tenant_id             int                                      null
) CHARSET = utf8;


create table t_transformation_sql
(
    id           int auto_increment
        primary key,
    data_pool_id int                                   null comment '数据池Id',
    name         varchar(256)                          null comment '名称',
    sql_content  text                                  null comment '运行Sql',
    deleted      int         default 0                 null comment '0：未删除，1：已删除',
    create_time  datetime(0) default CURRENT_TIMESTAMP null,
    update_time  datetime(0)                           null on update CURRENT_TIMESTAMP,
    tenant_id    int                                   null,
    `status`   varchar(256) null
) CHARSET = utf8;



create table t_transformation_run_log
(
    id                  int auto_increment
        primary key,
    data_pool_id        int                                       null comment '数据池Id',
    transformation_id   int                                       null,
    transformation_name varchar(128) default null,
    error_message       text                                      null,
    sql_content         text                                      null comment '运行Sql',
    async_flag          tinyint(1)                                null,
    time_scope          int                                       null comment '吞吐时间： 单位秒',
    success_flag        int          default 0                    null comment '0：成功，1：不成功',
    deleted             int          default 0                    null comment '0：未删除，1：已删除',
    create_time         datetime(0)  default CURRENT_TIMESTAMP(0) null,
    tenant_id           int                                       null
) CHARSET = utf8;



CREATE TABLE `t_kpi_param`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `level_name`  varchar(255) DEFAULT NULL,
    `name`        varchar(255) DEFAULT NULL,
    `type`        tinyint(4)   DEFAULT '1' COMMENT '见DataTypeEnum',
    `kpi_id`      int(11)      DEFAULT NULL,
    `deleted`     tinyint(4)   DEFAULT '0',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP,
    `tenant_id`   int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARSET = utf8;

alter table t_business_topic_kpi
    add column `type` tinyint(4) DEFAULT '0' COMMENT '0:默认的知识模型里的kpi，1：参数设置里的kpi';

CREATE TABLE `t_topic_param`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `name`        varchar(255)  DEFAULT NULL,
    `type`        int(11)       DEFAULT '0' COMMENT '0:text，1：静态变量',
    `topic_id`    int(11)       DEFAULT NULL,
    `value`       varchar(5000) DEFAULT NULL,
    `deleted`     tinyint(4)    DEFAULT '0',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP,
    `tenant_id`   int(11)       DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARSET = utf8;

CREATE TABLE `t_olap_kpi`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `component_id` int(11)    DEFAULT NULL,
    `kpi_id`       int(11)    DEFAULT NULL,
    `deleted`      tinyint(4) DEFAULT '0',
    `create_time`  datetime   DEFAULT CURRENT_TIMESTAMP,
    `update_time`  datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `tenant_id`    int(11)    DEFAULT NULL,
    `type`         int(2)     DEFAULT NULL,
    `sort_order`   int(11)    DEFAULT NULL,
    `sort_type`    tinyint(4) DEFAULT NULL,
    PRIMARY KEY (`id`)
) CHARSET = utf8;


CREATE TABLE `t_component_config`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT,
    `component_id`     int(11)    DEFAULT NULL,
    `component_value`  text       DEFAULT NULL,
    `component_config` text       DEFAULT NULL,
    `deleted`          tinyint(4) DEFAULT '0',
    `create_time`      datetime   DEFAULT CURRENT_TIMESTAMP,
    `update_time`      datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `tenant_id`        int        DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARSET = utf8;


-- auto-generated definition
DROP TABLE IF EXISTS `t_process_tree_kpi`;
create table t_process_tree_kpi
(
    id            int auto_increment
        primary key,
    topic_id      int                                null comment 'topicId',
    name          varchar(64)                        null comment '名称',
    colour_kpi_id int                                null comment '绑定颜色的KPIId',
    icon          varchar(512)                       null comment 'icon',
    colour        varchar(8192)                      null comment '颜色信息',
    create_time   datetime default CURRENT_TIMESTAMP null,
    update_time   datetime                           null on update CURRENT_TIMESTAMP,
    deleted       tinyint  default 0                 null comment '0：未删除，1：已删除',
    tenant_id     int                                null comment '租户ID'
) CHARSET = utf8 comment '流程图Kpi'
  collate = utf8_general_ci;


-- auto-generated definition
DROP TABLE IF EXISTS `t_process_tree_kpi_relation`;
create table t_process_tree_kpi_relation
(
    id                  int auto_increment
        primary key,
    process_tree_kpi_id int                                null comment '流程图KpiId',
    name                varchar(128)                       null comment '名字',
    type                int                                null comment '类型 0：事件KPI， 1，连线KPI',
    kpi_id              int                                null comment 'KPI id',
    create_time         datetime default CURRENT_TIMESTAMP null,
    update_time         datetime                           null on update CURRENT_TIMESTAMP,
    deleted             tinyint  default 0                 null comment '0：未删除，1：已删除',
    tenant_id           int                                null comment '租户ID'
) CHARSET = utf8 comment '流程图Kpi关系'
  collate = utf8_general_ci;


alter table t_sheet_component
    add column json_value varchar(512) DEFAULT NULL;


alter table t_data_extractor
    add column `update_type` tinyint(4) DEFAULT '1' COMMENT '1：增量更新（默认），2：全量更新';
alter table t_topic_filter
    add column `file_name` varchar(500) DEFAULT NULL;
alter table t_topic_filter
    add column `column_name` varchar(500) DEFAULT NULL;
alter table t_topic_sheet_variable
    add column `file_name` varchar(500) DEFAULT NULL;
alter table t_kpi_relation
    add column `file_name` varchar(500) DEFAULT NULL;
alter table t_kpi_relation
    add column `column_name` varchar(500) DEFAULT NULL;
alter table t_cause_related_variables
    add column `file_name` varchar(500) DEFAULT NULL;

CREATE TABLE `t_topic_filter_mark`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)      DEFAULT NULL,
    `deleted`     tinyint(4)   DEFAULT '0' COMMENT '0：未删除，1：已删除',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `num`         int(11)      DEFAULT NULL,
    `time`        datetime     DEFAULT NULL,
    `tenant_id`   int(11)      DEFAULT NULL,
    `name`        varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARSET = utf8;

CREATE TABLE `t_topic_filter_bookmark`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `mark_id`     int(11)    DEFAULT NULL,
    `filter_id`   int(11)    DEFAULT NULL,
    `deleted`     tinyint(4) DEFAULT '0',
    `create_time` datetime   DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `tenant_id`   int(11)    DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARSET = utf8;


-- auto-generated definition
create table t_task_executor
(
    id                 int auto_increment
        primary key,
    status             int                                  null comment '状态: 0，暂停，1，开启，2，已经执行,3 正在执行',
    source             int                                  null comment '任务来源: 0 用户创建，1 任务生成，',
    parent_id          int                                  null comment '父任务Id',
    data_task_id       int                                  null,
    data_task_child_id int                                  null comment '数据任务孩子Id',
    task_name          varchar(128)                         null,
    task_enum          varchar(32)                          null comment '任务枚举',
    task_info          text                                 null comment '任务信息',
    deleted            tinyint    default 0                 null comment '0：未删除，1：已删除',
    create_time        datetime   default CURRENT_TIMESTAMP null,
    update_time        datetime                             null on update CURRENT_TIMESTAMP,
    tenant_id          int                                  null comment '租户ID',
    immediately_flag   tinyint(1) default 0                 null comment '是否立即执行：0 false ，1 true'
) CHARSET = utf8 comment '任务执行信息'
  CHARSET = utf8;


-- auto-generated definition
DROP TABLE IF EXISTS `t_process_tree_kpi`;
create table t_process_tree_kpi
(
    id                       int auto_increment
        primary key,
    topic_id                 int                                null comment 'topicId',
    name                     varchar(64)                        null comment '名称',
    icon                     varchar(512)                       null comment 'icon',
    event_colour             text                               null comment '事件颜色信息',
    line_colour              text                               null comment '连线颜色信息',
    bond_colour_event_kpi_id int                                null comment '绑定事件颜色的KPIId',
    bond_colour_line_kpi_id  int                                null comment '绑定连线颜色的KPIId',
    create_time              datetime default CURRENT_TIMESTAMP null,
    update_time              datetime                           null on update CURRENT_TIMESTAMP,
    deleted                  tinyint  default 0                 null comment '0：未删除，1：已删除',
    tenant_id                int                                null comment '租户ID',
    colour_line_kpi_id       int                                null comment '线KPI颜色Id'
) CHARSET = utf8 comment '流程图Kpi'
  CHARSET = utf8;


-- auto-generated definition
create table t_data_task_child
(
    id           int auto_increment
        primary key,
    pool_id      int                                null,
    name         varchar(255)                       null,
    task_type    int                                null comment '数据任务类型：0 数据提取，1数据转换',
    binding_id   int                                null comment '根据数据类型绑定的Id,当数据类型为 0 时，为 data_extractorID，当为1时：transformationId',
    data_task_id int                                null comment '数据任务Id',
    state        int                                null comment '0运行，1，暂停',
    deleted      tinyint  default 0                 null comment '0：未删除，1：已删除',
    sort         int                                not null comment '排序',
    create_time  datetime default CURRENT_TIMESTAMP null,
    update_time  datetime                           null on update CURRENT_TIMESTAMP,
    tenant_id    int                                null
)
    comment '数据任务的子信息' CHARSET = utf8;


-- auto-generated definition
create table t_task_log
(
    id               int auto_increment
        primary key,
    data_task_id     int                                null,
    task_executor_id int                                null comment '任务Id',
    status           int                                null comment '状态: 0，暂停，1，开启，2，已经执行，3 正在执行， ',
    run_time         int                                null comment '运行时间',
    log_info         text                               null comment '日志信息',
    task_enum        varchar(32)                        null comment '任务枚举',
    start_time       datetime                           null on update CURRENT_TIMESTAMP,
    create_time      datetime default CURRENT_TIMESTAMP null,
    update_time      datetime                           null on update CURRENT_TIMESTAMP,
    tenant_id        int                                null comment '租户ID',
    task_name        varchar(256)                       null,
    run_type         int                                null comment '运行类型 0 ： 自动运行， 1手动运行 默认为0；'
)
    comment '任务日志' CHARSET = utf8;


-- auto-generated definition
create table t_lineage_relation
(
    id              int auto_increment
        primary key,
    relationship_id int                                null comment '关系Id',
    to_table        varchar(128)                       null,
    relation_type   int                                null comment '类型 0：etl',
    create_time     datetime default CURRENT_TIMESTAMP null,
    update_time     datetime                           null on update CURRENT_TIMESTAMP,
    deleted         tinyint  default 0                 null comment '0：未删除，1：已删除',
    tenant_id       int                                null comment '租户ID',
    binding_id      int                                null comment '根据不同的类型可以绑定iD，当type为0时：transformationId',
    from_table      varchar(128)                       null
)
    comment '血缘关系' CHARSET = utf8;



drop table if exists t_component_edit_lock;
-- auto-generated definition
create table t_component_edit_lock
(
    id               int auto_increment
        primary key,
    hold_id          int                                null comment '最后持有人ID',
    hold_name        varchar(128)                       null comment '最后持有人名称',
    lock_component_id varchar(128)                       null ,
    edit_lock_enum varchar(128)                       null ,
    last_update_time datetime                           null comment '最后心跳更新',
    create_time      datetime default CURRENT_TIMESTAMP null,
    update_time      datetime                           null on update CURRENT_TIMESTAMP,
    tenant_id        int                                null comment '租户ID'
)
    comment '组件编辑锁' collate = utf8_general_ci;



ALTER TABLE t_task_executor
    add releation_id int(11) DEFAULT NULL COMMENT '关联ID';
ALTER TABLE t_simulation_run_result
    add COLUMN data_task_id int(11) DEFAULT NULL COMMENT '任务Id';


create table sp_process.t_model_index
(
    model_id         int(11)      null,
    index_name       varchar(500) null,
    spark_table_name varchar(500) null,
    index_id         int(11)      NOT NULL AUTO_INCREMENT,
    constraint key_name primary key (index_id)

) CHARSET = utf8;

alter table sp_process.t_model_index
    add column create_time datetime NULL DEFAULT CURRENT_TIMESTAMP(0);


CREATE TABLE `t_data_model_sort`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `data_model_id` int(11)    DEFAULT NULL,
    `sorting`       int(11)    DEFAULT NULL,
    `create_time`   datetime   DEFAULT CURRENT_TIMESTAMP,
    `update_time`   datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted`       tinyint(4) DEFAULT '0',
    `tenant_id`     int(11)    DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8;

ALTER TABLE t_topic_filter
    add COLUMN param_values text COMMENT 'newsheet里属性过滤选中的值，此字段有值，且type=2000，表示topicFilter不允许跳转';

ALTER TABLE t_topic_filter
    add COLUMN user_id int(11) DEFAULT NULL COMMENT '绑定userid';

ALTER TABLE t_topic_filter
    add COLUMN mark_type TINYINT(4) DEFAULT '0' COMMENT '0:原始filter，1：bookmark的filter';



alter table t_topic_snapshot_log
    add release_type int default 0 null comment '发布类型，0 发布， 1 取消发布' after create_user;


CREATE TABLE `t_sheet_param`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `sheet_id`    int(11)      DEFAULT NULL,
    `type`        int(11)      DEFAULT NULL COMMENT '10:瓶颈分析流程时长',
    `param`       varchar(500) DEFAULT NULL,
    `param_value` text CHARACTER SET utf8mb4,
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted`     tinyint(4)   DEFAULT '0',
    `tenant_id`   int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;


CREATE TABLE `t_topic_view_record`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `topic_id`    int(11)    DEFAULT NULL,
    `user_id`     int(11)    DEFAULT NULL,
    `create_time` datetime   DEFAULT CURRENT_TIMESTAMP,
    `deleted`     tinyint(4) DEFAULT '0',
    `tenant_id`   int(11)    DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;



CREATE TABLE `t_data_model_record`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `data_model_id` int(11)    DEFAULT NULL,
    `type`          int(11)    DEFAULT NULL,
    `create_time`   datetime   DEFAULT CURRENT_TIMESTAMP,
    `deleted`       tinyint(4) DEFAULT '0',
    `user_id`       int(11)    DEFAULT NULL,
    `tenant_id`     int(11)    DEFAULT NULL,
    `before_info`   text,
    `aflter_info`   text,
    `update_time`   datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB;


CREATE TABLE `t_topic_config`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `topic_id`     int(11)    DEFAULT NULL,
    `max_export`   bigint     DEFAULT NULL,
    `create_time`  datetime   DEFAULT CURRENT_TIMESTAMP,
    `deleted`      tinyint(4) DEFAULT '0',
    `update_time`  datetime   DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `tenant_id`    int(11)    DEFAULT NULL,
    `allow_export` tinyint    DEFAULT '0' COMMENT '0：不允许导出，1：允许导出',
    PRIMARY KEY (`id`),
    UNIQUE KEY `topic_id` (`topic_id`) USING BTREE
) ENGINE = InnoDB;


-- 2.8.6 --------------------------------

alter table t_simulation
    modify name varchar(255) null comment '执行动作名称';


alter table t_simulation_event
    modify name varchar(255) null comment '事件名称';

alter table t_simulation_programme
    modify name varchar(255) null comment '方案名称';

alter table t_simulation_resource
    modify name varchar(255) null comment '方案名称';

alter table t_simulation_resource_pool
    modify name varchar(255) null comment '资源名称';


alter table t_simulation_run_frequency
    modify name varchar(255) null comment '资源名称';

CREATE TABLE `t_allow_process`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `topic_id`      int(11)      DEFAULT NULL,
    `sheet_id`      int(11)      DEFAULT NULL,
    `result`        varchar(500) DEFAULT NULL,
    `result_number` int(11)      DEFAULT NULL,
    `deleted`       tinyint(4)   DEFAULT '0',
    `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP,
    `user_id`       int(11)      DEFAULT NULL,
    `tenant_id`     int          DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


-- 2.8.7 --------------------------------

alter table t_topic_filter
    add filter_event varchar(6000) null;

alter table t_topic_filter
    add event_column varchar(256) null;



ALTER TABLE t_topic_filter_mark
    ADD COLUMN type TINYINT(4) DEFAULT 0;

ALTER TABLE t_data_connector_jdbc
    ADD COLUMN instance_number int(11) DEFAULT NULL;



-- 2.8.8
-- auto-generated definition
create table t_process_manage
(
    id                     int auto_increment
        primary key,
    process_name           varchar(255)                         null,
    level                  int                                  null,
    parent_id              int        default 0                 null,
    release_flag           tinyint(1) default 0                 null,
    discard_flag           tinyint(1) default 0                 null,
    process_code           varchar(255)                         null,
    responsible_department varchar(255)                         null,
    version_code           varchar(255)                         null,
    file_id                int        default 0                 null,
    deleted                int        default 0                 null,
    release_time           datetime                             null,
    create_time            datetime   default CURRENT_TIMESTAMP null,
    update_time            datetime                             null on update CURRENT_TIMESTAMP,
    tenant_id              int                                  null,
    best_practice_flag     tinyint(1) default 0                 null,
    responsible_person_ids  varchar(2000)                        null,
    description            text                                 null,
    datum_library_flag     tinyint(1) default 0                 null
);

-- 2.8.9

alter table cloud_file_info
    add local_storage tinyint(1) default 0 null;



alter table t_process_manage
    add scope varchar(500) null;

alter table t_process_manage
    add business_objectives varchar(500) null;


alter table t_process_manage
    add kpi_indicators varchar(500) null;

alter table t_process_manage
    add remarks varchar(500) null;

alter table t_process_manage
    add draft_flag tinyint(1) default 0 null;


alter table cloud_file_info
    add storage_type tinyint default 0 null ;


alter table t_process_manage
    add operation_monitoring varchar(100) null;


alter table t_process_manage
    add process_input varchar(100) null;

alter table t_process_manage
    add process_output varchar(100) null;

alter table t_process_manage
    add design_it_systems varchar(100) null;

alter table t_process_manage
    add critical_control_points varchar(100) null;


create table t_process_event
(
    id                       int auto_increment primary key,
    process_manage_id               int                         null comment '流程Id',
    process_manage_probe_id        int                          null comment '当该事件有流程下探的时候',
    `name`                     varchar(64)                      null comment '事件名称',
    event_type               int      default 0                 null comment '事件类型，-2：结束 -1：开始， 0:事件 ，1：专属网关（开始），2：并行网关（开始）， 11：专属网关（结束），12：并行网关（结束)',
    element_id               varchar(64)                        null comment 'bpmn事件Id',
    deleted                  tinyint  default 0                 null comment '0：未删除，1：已删除',
    create_time              datetime default CURRENT_TIMESTAMP null,
    update_time              datetime                           null on update CURRENT_TIMESTAMP,
    tenant_id                int                                null comment '租户ID'
) comment '流程管理事件' ;

ALTER TABLE t_data_model_load_log ADD COLUMN msg text ;


CREATE TABLE IF NOT EXISTS t_sa_token_info
(
    id             int auto_increment
        primary key,
    session_flag   tinyint default 0 null,
    serialize_flag tinyint           null,
    map_key        varchar(256)      null,
    map_value      text              null,
    expired_time   varchar(128)      null,
    update_time    datetime          null on update CURRENT_TIMESTAMP,
    tenant_id      int               null
)
    charset = utf8;




CREATE TABLE `t_execution_action_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `time` datetime DEFAULT NULL,
  `signal_rule_id` int DEFAULT NULL,
  `opmitization_id` int DEFAULT NULL,
  `execution_action_id` int DEFAULT NULL,
  `signal_count` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_signal_rule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) DEFAULT NULL,
  `model_id` int DEFAULT NULL,
  `status` tinyint DEFAULT '2',
  `execution_time` datetime DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `create_user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_optimization_signal` (
  `id` int NOT NULL AUTO_INCREMENT,
  `signal_rule_id` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_signal_column` (
  `id` int NOT NULL AUTO_INCREMENT,
  `optimization_signal_id` int DEFAULT NULL,
  `expression` varchar(10000) DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_signal_condition` (
  `id` int NOT NULL AUTO_INCREMENT,
  `optimization_signal_id` int DEFAULT NULL,
  `expression` varchar(10000) DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;


CREATE TABLE `t_execution_action` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) DEFAULT NULL,
  `topic_id` int DEFAULT NULL,
  `action_id` int DEFAULT NULL,
  `type` tinyint DEFAULT NULL COMMENT '1：手动，2：自动',
  `signal_rule_id` int DEFAULT NULL,
  `execution_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_execution_action_param` (
  `id` int NOT NULL AUTO_INCREMENT,
  `execution_action_id` int DEFAULT NULL,
  `param` varchar(500) DEFAULT NULL,
  `param_value` varchar(1000) DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ;




CREATE TABLE `sys_quartz_job` (
  `id` varchar(255)  NOT NULL ,
  `create_by` varchar(255)  DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `del_flag` int DEFAULT '0' COMMENT '删除状态',
  `update_by` varchar(255)  DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `job_class_name` varchar(255)  DEFAULT NULL COMMENT '任务类名',
  `cron_expression` varchar(255)  DEFAULT NULL COMMENT 'cron表达式',
  `parameter` varchar(500)  DEFAULT NULL COMMENT '参数',
  `meeting_record_id` int DEFAULT NULL COMMENT '会议室记录id',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `status` int DEFAULT '0' COMMENT '状态 0正常 -1停止',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;




CREATE TABLE QRTZ_JOB_DETAILS(
SCHED_NAME VARCHAR(120) NOT NULL,
JOB_NAME VARCHAR(190) NOT NULL,
JOB_GROUP VARCHAR(190) NOT NULL,
DESCRIPTION VARCHAR(250) NULL,
JOB_CLASS_NAME VARCHAR(250) NOT NULL,
IS_DURABLE VARCHAR(1) NOT NULL,
IS_NONCONCURRENT VARCHAR(1) NOT NULL,
IS_UPDATE_DATA VARCHAR(1) NOT NULL,
REQUESTS_RECOVERY VARCHAR(1) NOT NULL,
JOB_DATA BLOB NULL,
PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_TRIGGERS (
SCHED_NAME VARCHAR(120) NOT NULL,
TRIGGER_NAME VARCHAR(190) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
JOB_NAME VARCHAR(190) NOT NULL,
JOB_GROUP VARCHAR(190) NOT NULL,
DESCRIPTION VARCHAR(250) NULL,
NEXT_FIRE_TIME BIGINT(13) NULL,
PREV_FIRE_TIME BIGINT(13) NULL,
PRIORITY INTEGER NULL,
TRIGGER_STATE VARCHAR(16) NOT NULL,
TRIGGER_TYPE VARCHAR(8) NOT NULL,
START_TIME BIGINT(13) NOT NULL,
END_TIME BIGINT(13) NULL,
CALENDAR_NAME VARCHAR(190) NULL,
MISFIRE_INSTR SMALLINT(2) NULL,
JOB_DATA BLOB NULL,
PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
FOREIGN KEY (SCHED_NAME,JOB_NAME,JOB_GROUP)
REFERENCES QRTZ_JOB_DETAILS(SCHED_NAME,JOB_NAME,JOB_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_SIMPLE_TRIGGERS (
SCHED_NAME VARCHAR(120) NOT NULL,
TRIGGER_NAME VARCHAR(190) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
REPEAT_COUNT BIGINT(7) NOT NULL,
REPEAT_INTERVAL BIGINT(12) NOT NULL,
TIMES_TRIGGERED BIGINT(10) NOT NULL,
PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
REFERENCES QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_CRON_TRIGGERS (
SCHED_NAME VARCHAR(120) NOT NULL,
TRIGGER_NAME VARCHAR(190) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
CRON_EXPRESSION VARCHAR(120) NOT NULL,
TIME_ZONE_ID VARCHAR(80),
PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
REFERENCES QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_SIMPROP_TRIGGERS
  (
    SCHED_NAME VARCHAR(120) NOT NULL,
    TRIGGER_NAME VARCHAR(190) NOT NULL,
    TRIGGER_GROUP VARCHAR(190) NOT NULL,
    STR_PROP_1 VARCHAR(512) NULL,
    STR_PROP_2 VARCHAR(512) NULL,
    STR_PROP_3 VARCHAR(512) NULL,
    INT_PROP_1 INT NULL,
    INT_PROP_2 INT NULL,
    LONG_PROP_1 BIGINT NULL,
    LONG_PROP_2 BIGINT NULL,
    DEC_PROP_1 NUMERIC(13,4) NULL,
    DEC_PROP_2 NUMERIC(13,4) NULL,
    BOOL_PROP_1 VARCHAR(1) NULL,
    BOOL_PROP_2 VARCHAR(1) NULL,
    PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
    REFERENCES QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_BLOB_TRIGGERS (
SCHED_NAME VARCHAR(120) NOT NULL,
TRIGGER_NAME VARCHAR(190) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
BLOB_DATA BLOB NULL,
PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
INDEX (SCHED_NAME,TRIGGER_NAME, TRIGGER_GROUP),
FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
REFERENCES QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_CALENDARS (
SCHED_NAME VARCHAR(120) NOT NULL,
CALENDAR_NAME VARCHAR(190) NOT NULL,
CALENDAR BLOB NOT NULL,
PRIMARY KEY (SCHED_NAME,CALENDAR_NAME))
ENGINE=InnoDB;

CREATE TABLE QRTZ_PAUSED_TRIGGER_GRPS (
SCHED_NAME VARCHAR(120) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
PRIMARY KEY (SCHED_NAME,TRIGGER_GROUP))
ENGINE=InnoDB;

CREATE TABLE QRTZ_FIRED_TRIGGERS (
SCHED_NAME VARCHAR(120) NOT NULL,
ENTRY_ID VARCHAR(95) NOT NULL,
TRIGGER_NAME VARCHAR(190) NOT NULL,
TRIGGER_GROUP VARCHAR(190) NOT NULL,
INSTANCE_NAME VARCHAR(190) NOT NULL,
FIRED_TIME BIGINT(13) NOT NULL,
SCHED_TIME BIGINT(13) NOT NULL,
PRIORITY INTEGER NOT NULL,
STATE VARCHAR(16) NOT NULL,
JOB_NAME VARCHAR(190) NULL,
JOB_GROUP VARCHAR(190) NULL,
IS_NONCONCURRENT VARCHAR(1) NULL,
REQUESTS_RECOVERY VARCHAR(1) NULL,
PRIMARY KEY (SCHED_NAME,ENTRY_ID))
ENGINE=InnoDB;

CREATE TABLE QRTZ_SCHEDULER_STATE (
SCHED_NAME VARCHAR(120) NOT NULL,
INSTANCE_NAME VARCHAR(190) NOT NULL,
LAST_CHECKIN_TIME BIGINT(13) NOT NULL,
CHECKIN_INTERVAL BIGINT(13) NOT NULL,
PRIMARY KEY (SCHED_NAME,INSTANCE_NAME))
ENGINE=InnoDB;

CREATE TABLE QRTZ_LOCKS (
SCHED_NAME VARCHAR(120) NOT NULL,
LOCK_NAME VARCHAR(40) NOT NULL,
PRIMARY KEY (SCHED_NAME,LOCK_NAME))
ENGINE=InnoDB;

CREATE INDEX IDX_QRTZ_J_REQ_RECOVERY ON QRTZ_JOB_DETAILS(SCHED_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_J_GRP ON QRTZ_JOB_DETAILS(SCHED_NAME,JOB_GROUP);

CREATE INDEX IDX_QRTZ_T_J ON QRTZ_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_JG ON QRTZ_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_C ON QRTZ_TRIGGERS(SCHED_NAME,CALENDAR_NAME);
CREATE INDEX IDX_QRTZ_T_G ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_T_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_G_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NEXT_FIRE_TIME ON QRTZ_TRIGGERS(SCHED_NAME,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_MISFIRE ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE_GRP ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_GROUP,TRIGGER_STATE);

CREATE INDEX IDX_QRTZ_FT_TRIG_INST_NAME ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME);
CREATE INDEX IDX_QRTZ_FT_INST_JOB_REQ_RCVRY ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_FT_J_G ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_JG ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_T_G ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_FT_TG ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);


alter table t_topic_sheet
    add snapshot_parent_id int default 0 null;

alter table t_topic_sheet
  add `config_map`  varchar(255) DEFAULT NULL;


alter table t_task_executor
    add task_info_flag varchar(512) null;

ALTER TABLE t_signal_rule ADD COLUMN data_type TINYINT(4) DEFAULT 1;

alter table t_data_task
    add next_execution_time bigint null;

create table t_sub_process_manage
(
    id                        int auto_increment primary key,
    element_id                varchar(255)                       null,
    process_manage_id         int      default 0                 null ,
    sub_process_manage_id int      default 0                 null ,
    create_time               datetime default CURRENT_TIMESTAMP null,
    tenant_id                 int                                null
) ENGINE=InnoDB;

ALTER TABLE t_topic_filter MODIFY COLUMN expression longtext DEFAULT NULL;

create table t_audit_log
(
    id          int auto_increment
        primary key,
    path        varchar(256)                       null comment '接口地址',
    user_name   varchar(256)                       null comment '用户名称',
    user_id     int                                null comment '用户Id',
    ip          varchar(64)                        null comment 'IP地址',
    create_time datetime default CURRENT_TIMESTAMP null,
    tenant_id   int                                null comment '租户ID',
    parameter   varchar(5000)                      null comment '请求参数'
)ENGINE=InnoDB;

ALTER TABLE t_topic_filter ADD COLUMN description VARCHAR(1000) DEFAULT NULL;


create table t_sap_log
(
    id          int auto_increment
        primary key,
    info   varchar(5000)                      null comment '信息',
    load_type   varchar(64)                        null comment '加载类型',
    create_time datetime default CURRENT_TIMESTAMP null,
    run_sql     varchar(5000)                      null,
    tenant_id   int                                null comment '租户ID'
)ENGINE=InnoDB;

alter table t_file
    add parquet_length bigint unsigned null;


alter table t_audit_log
    add operate_type int null;

ALTER TABLE t_topic_sheet_new MODIFY components_value LONGTEXT ;

CREATE TABLE `t_topic_config_color` (
  `id` int NOT NULL AUTO_INCREMENT,
  `topic_id` int DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `color_template` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

ALTER TABLE t_topic_config ADD COLUMN `active_color_template` varchar(128) DEFAULT NULL;


CREATE TABLE `t_water_mark_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `water_mark_enabled` tinyint DEFAULT NULL,
  `word` int DEFAULT NULL,
  `font_size` int DEFAULT NULL,
  `transparency` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  PRIMARY KEY (`id`)
);

ALTER TABLE t_topic_filter_mark add COLUMN index_id int DEFAULT NULL;

ALTER TABLE t_topic_filter_mark ADD UNIQUE INDEX topic_id_index_id (topic_id,index_id,deleted);


alter table t_data_model_load_log
    add `version_number` int null;

alter table t_data_model
    add `max_version` int  default 0 null;

alter table t_data_model
    add `current_version` int  default 0 null;


ALTER TABLE t_allow_process add COLUMN reason_type TINYINT DEFAULT 1;

ALTER TABLE t_allow_process add COLUMN condition_name VARCHAR(255) DEFAULT NULL;

CREATE TABLE `t_sheet_dimension` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sheet_id` int DEFAULT NULL,
  `kpi_id` int DEFAULT NULL,
  `title_type` tinyint DEFAULT NULL,
  `table_name` varchar(255)  DEFAULT NULL,
  `column_name` varchar(255)  DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint DEFAULT '0',
  `tenant_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
);


alter table t_data_model_load_log
    add model_status varchar(64) null;


alter table t_simulation
    add data_model_id int null;



alter table t_simulation_event_attr
    add run_cost_expression varchar(4000) null;

alter table t_simulation_event_attr
    add run_cost_formatting varchar(500) null;

alter table t_simulation_event_attr
    add run_cost_format varchar(500) null;


alter table t_simulation_event_attr
    add run_time_expression varchar(4000) null;

alter table t_simulation_event_attr
    add run_time_formatting varchar(500) null;

alter table t_simulation_event_attr
    add run_time_format varchar(500) null;


alter table t_simulation_run_frequency
    add expression varchar(5000) null;

alter table t_simulation_run_frequency
    add formatting varchar(5000) null;

alter table t_simulation_run_frequency
    add format varchar(5000) null;


alter table t_simulation_resource
    add number_expression varchar(2000) null;

alter table t_simulation_resource
    add number_formatting varchar(500) null;

alter table t_simulation_resource
    add number_format varchar(500) null;


alter table t_simulation_resource
    add resource_cost_expression varchar(2000) null;

alter table t_simulation_resource
    add resource_cost_formatting varchar(500) null;

alter table t_simulation_resource
    add resource_cost_format varchar(500) null;



alter table t_kpi ADD COLUMN formatting varchar(64) null;
alter table t_kpi ADD COLUMN format varchar(64) null;
alter table t_kpi ADD COLUMN column_type varchar(64) null;
alter table t_topic_filter ADD COLUMN formatting varchar(64) null;
alter table t_topic_filter ADD COLUMN format varchar(64) null;
alter table t_signal_column ADD COLUMN formatting varchar(64) null;
alter table t_signal_column ADD COLUMN format varchar(64) null;
alter table t_signal_condition ADD COLUMN formatting varchar(64) null;
alter table t_signal_condition ADD COLUMN format varchar(64) null;


CREATE TABLE `t_newsheet_template` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `sheet_name` varchar(255) DEFAULT NULL,
  `info` longtext ,
  `introduction` text,
  `deleted` tinyint DEFAULT '0',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `t_newsheet_template_parameter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `template_id` int DEFAULT NULL,
  `origin_refer` text  DEFAULT NULL,
  `origin_refer_expression` text  DEFAULT NULL,
  `origin_expression` text  DEFAULT NULL,
  `parameter` varchar(255) DEFAULT NULL,
  `introduction` varchar(500) DEFAULT NULL,
  `deleted` tinyint DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` int DEFAULT NULL,
  `type` tinyint DEFAULT '1',
  `kpi_param` varchar(255) DEFAULT NULL,
  `column_type` varchar(255) DEFAULT NULL,
  `format` varchar(255) DEFAULT NULL,
  `formatting` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

ALTER TABLE t_topic_filter add COLUMN extension longtext DEFAULT NULL;

alter table t_data_task
    add cron_expression varchar(128) null;


create table user_profiles
(
  `id`      int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `k`     text DEFAULT NULL,
  `v`   text DEFAULT NULL,
  `deleted`     tinyint(4) not  NULL DEFAULT 0 ,
  PRIMARY KEY (`id`)
);

CREATE TABLE `t_temp_pql_table`
(
  `id`              int(11) NOT NULL AUTO_INCREMENT,
  `topic_id`        int(11) NOT NULL,
  `name`            varchar(256) NOT NULL,
  `table_def`       text NOT NULL,
  `deleted`         boolean not null default 0,
  `created_at`      timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at`      timestamp DEFAULT CURRENT_TIMESTAMP,
  `tenant_id`       int DEFAULT NULL,
  PRIMARY KEY (`id`)
);


alter table t_data_model add column bucket_groups text default null;

alter table t_task_executor
    add pool_id int null;


alter table t_task_log
    add pool_id int null;

ALTER TABLE t_topic_sheet_variable ADD COLUMN `ref_param` VARCHAR(255)  DEFAULT NULL COMMENT '引用的 kpi 定义';

ALTER TABLE t_newsheet_template ADD COLUMN `temp_table_config` text  DEFAULT NULL COMMENT '引用的临时表定义';

create table t_ai_prompts
(
  id            int auto_increment
    primary key,
  prompts_type  varchar(128)                       null,
  prompts_value text                               null,
  model_config  text                               null,
  deleted       tinyint  default 0                 null comment '0：未删除，1：已删除',
  create_time   datetime default CURRENT_TIMESTAMP null,
  update_time   datetime                           null on update CURRENT_TIMESTAMP,
  tenant_id     int                                null,
  is_template   tinyint(1)                         null
);


ALTER TABLE t_topic_config ADD COLUMN `workday_enable` tinyint DEFAULT NULL COMMENT '是否开启工作时间';
ALTER TABLE t_topic_config ADD COLUMN `workday_table_name` VARCHAR(64) DEFAULT NULL COMMENT '工作时间表名';


ALTER TABLE sp_process.t_signal_rule ADD COLUMN `signal_limit` int  DEFAULT 100;

ALTER TABLE sp_process.t_signal_column ADD COLUMN `unit`  VARCHAR(128)  DEFAULT NULL COMMENT 'unit';

ALTER TABLE sp_process.t_newsheet_template ADD COLUMN `user_name` VARCHAR(128)  DEFAULT NULL COMMENT '发布人';


CREATE TABLE sp_process.t_customer_root_cause
(
  `id`              int(11)      NOT NULL AUTO_INCREMENT,
  `topic_sheet_id`  int(11)      NULL DEFAULT NULL,
  `deleted`         int(11)      NULL DEFAULT 0 COMMENT '0：未删除，1：已删除',
  `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  `expression_name` varchar(255) NOT NULL,
  `expression`      text         NOT NULL,
  `tenant_id`       int(11)      NULL DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
);



ALTER TABLE t_data_connector
    modify `connect_link` varchar(10000) DEFAULT NULL COMMENT '自定义链接';


ALTER TABLE t_data_connector MODIFY COLUMN connect_link longtext DEFAULT NULL;


ALTER TABLE sp_process.t_data_model add COLUMN `start_time` int(11) DEFAULT NULL;

ALTER TABLE sp_process.t_topic_filter add COLUMN `start_time` VARCHAR(64) DEFAULT NULL;
ALTER TABLE sp_process.t_topic_filter add COLUMN `end_time` VARCHAR(64) DEFAULT NULL;


CREATE TABLE `t_topic_multi_process` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `topic_id` int(11) NOT NULL,
                                         `process_name` varchar(255),
                                         `event_name_column` varchar(255),
                                         `start_time_column` varchar(255),
                                         `end_time_column` varchar(255),
                                         `event_index` int COMMENT '事件列下标（如第一个事件列，则为 1）',
                                         `deleted` int COMMENT '0：未删除，1：已删除',
                                         `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                         `tenant_id`       int(11)      NULL DEFAULT NULL COMMENT '租户ID',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='topic多流程图';

ALTER TABLE sp_process.t_topic_multi_process add COLUMN `enable_multi_process` tinyint DEFAULT NULL COMMENT '是否开启多级流程图';


CREATE TABLE `t_topic_config_global` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `topic_id` int(11) NOT NULL,
                                         `description` varchar(255),
                                         `event_column` varchar(255),
                                         `format` varchar(64),
                                         `formatting` varchar(64),
                                         `deleted` int(11) COMMENT '0：未删除，1：已删除',
                                         `create_time`     datetime(0)  NULL DEFAULT CURRENT_TIMESTAMP(0),
                                         `update_time`     datetime(0)  NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
                                         `tenant_id`       int(11)      NULL DEFAULT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='topic全局过滤设置';