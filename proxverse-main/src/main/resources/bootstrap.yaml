spring:
  application:
    name: proxverse-project
  messages:
    basename: i18n/messages
    encoding: utf8
server:
    port: 8888
    servlet:
        session:
            cookie:
                name: oidcResource
                http-only: false




---
spring:
  profiles: nacos-local
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        file-extension: yaml
        namespace: 23c591ed-5192-4fe3-b912-9fe71eeb81a9

---
spring:
  profiles: zl
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        group: group-dev
      config:
        server-addr: ************:8848
        file-extension: yaml


---
spring:
  profiles: test1
  cloud:
    nacos:
      discovery:
        server-addr: nacos.mid:8848
        namespace: test1
      config:
        server-addr: nacos.mid:8848
        file-extension: yaml
        namespace: 9c9b927d-8053-49d9-a5c2-f41639eaf96d

---
spring:
  profiles: test2
  cloud:
    nacos:
      discovery:
        server-addr: nacos.mid:8848
        namespace: test2
      config:
        server-addr: nacos.mid:8848
        file-extension: yaml
        namespace: fd9f8230-c014-4491-866f-b938eb44c2be


---
spring:
  profiles: pre
  cloud:
    nacos:
      discovery:
        server-addr: nacos.mid:8848
        namespace: pre
      config:
        server-addr: nacos.mid:8848
        file-extension: yaml
        namespace: pre

---
spring:
  profiles: prod
  cloud:
    nacos:
      discovery:
        server-addr: nacos.middle:8848
        namespace: prod
      config:
        server-addr: nacos.middle:8848
        file-extension: yaml
        namespace: prod


---
spring:
    profiles: private
    cloud:
        nacos:
            discovery:
                server-addr: 172.16.51.187:8848
                namespace: deloitte
            config:
                server-addr: 172.16.51.187:8848
                file-extension: yaml
                namespace: deloitte