spring:
  redis:
    host: localhost
    port: 6379
    timeout: 5000
    jedis:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 50
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 3000
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 2
#    password: f628a1cba25f82e46e2e3cd54516cfb176c440d8cfcbe14fc3f94995735d1c29a0f6

  #LDAP
  ldap:
    ldapEnabled: false
    urls: ldap://*************:389
    base: dc=example,dc=org
    username: cn=admin,dc=example,dc=org
    password: admin
    groupId: 1
    userBase: OU=理士国际
    groupBase: OU=理士国际
  # flyway
  flyway:
    enabled: true
    # sql编码
    encoding: utf-8
    # 版本文件路径
    locations: classpath:ddl/mysql/migration
    validate-on-migrate: true
    # 是否以当前数据为基线
    baselineOnMigrate: true
    # 当前数据为基线的时候版本号
    baselineVersion: 1.0.0
    # 是否允许执行比当前版本号低的版本sql
    outOfOrder: true
  application:
    name: pm-project
  messages:
    basename: i18n/messages
    encoding: utf8
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

  h2:
    console:
      enabled=true:
  datasource:
    dynamic:
      # 设置默认的数据源或数据源组，默认值即为master
      primary: engine
      # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      datasource:
        oauth2:
          username: root
          password: 123456
          url: ******************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        engine:
          username: root
          password: 123456
          url: *******************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        execution:
          username: root
          password: 123456
          url: *********************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        meta:
          username: root
          password: 123456
          url: *************************************************************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
  servlet:
    multipart:
      enabled: true
      max-file-size: 2000MB
      max-request-size: 2000MB


server:
  port: 8888
  sleuth:
    log:
      slf4j:
        enabled: true
    sampler:
      # 抽样率，默认0.1
      probability: 1.0



oauth2.license.path: /data/sp/file/license
logging:
  level:
    com.sp.spengine.mapper: debug
    root: info
    org.springframework.web.servlet.DispatcherServlet: DEBUG
    org.springframework.cloud.sleuth: DEBUG

csv.file.save.path: ${java.io.tmpdir}/upload/
csv.child.file.save.path: ${java.io.tmpdir}/upload//child/
del.file.path: ${java.io.tmpdir}
business.file.path: ${java.io.tmpdir}/export/
# csv.file.save.path: /data/sp/file/csv/
export.path: ${java.io.tmpdir}/export/

spring.cache.enabled: false
spark.store.path: ${java.io.tmpdir}/warehouse/
spark.metastore.jdbc.url: *************************************************************************************************************************************************************************
spark.metastore.jdbc.user:  root
spark.metastore.jdbc.password:  123456
spark.master: yarn-client



security:
  oauth2:
    client:
      client-id: client
      client-secret: secret
      access-token-uri: http://localhost:8888/oauth/token
      user-authorization-uri: http://localhost:8888/oauth/authorize
    resource:
      token-info-uri: http://localhost:8888/oauth/check_token
mybatis-plus:
  mapper-locations: classpath:mapper/**.xml

spark.conf: "{\"spark.sql.pql.planCacheEnabled\":\"false\", \"spark.sql.pql.preBucketNumberRows\": \"2500000\", \"spark.sql.files.openCostInBytes\":\"512M\",\"spark.sql.files.maxPartitionBytes\":\"512M\",\"spark.sql.pql.encodedColDict.encodeWithModelBuild\":\"true\", \"spark.master\":\"local[8]\"}"

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效 (三个月)
  timeout: 7776000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结 (7天)
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 每次清理过期数据间隔的时间 （单位: 秒） ，默认值30秒，设置为-1代表不启动定时清理
  data-refresh-period: 3600
  enable-mysql-memory: true


ai:
  server:
    url: "localhost:12345"

spark.sql.columnar.enabled: false
spark.sql.pql.columnCacheEnabled: false
spark.sql.pql.native.calc.enabled: false
spark.sql.pql.encodedColDict.encodeWithModelBuild: false
spark.sql.pql.lowCardColDict.encoding.enabled: false
spark.sql.pql.encodedColDict.encoding.enabled: false
spark.sql.pql.sourceTarget.encoding.enabled: false

#默认密码
prx.admin.local.initPassword: proxverse
#快速开始的csv文件默认发票文件
prx.dataModel.file.quickStartDefaultFile: proxverse_demo
# 上传文件的限制大小默认1GB
prx.dataModel.file.maxUploadSize: 1g
#快速开始的限制大小默认50MB
prx.dataModel.file.quickStartMaxUploadSize: 50m
#流程图显示的最大event默认500
prx.workbench.processTree.maxQueryEvents: 500
#流程图显示的最大connect默认500
prx.workbench.processTree.maxQueryConnects: 500
#日志保存的默认天数默认14天
prx.gateway.log.keepDays: 14
#echarts查询top数量
prx.workbench.component.echartsQueryTopCount: 1000
prx.admin.license.enabled: false

prx.pql.queryCache.enabled: false
prx.model.build.bucketGroupEnabled: true

spark.sql.starry.expressions.rewriteCountDistinctAsBitmap: false

prx.model.build.bucketGroupFailBack: true

prx.login.sso.tokenUrl: http://localhost:8888/decision/zsdl/token/validate
prx.login.loginToken: token

# 统一缓存框架配置
cache:
  # 缓存类型：guava（本地缓存）或 redis（分布式缓存）
  type: redis

  # 是否启用缓存统计
  enable-stats: true

  # 缓存键前缀
  key-prefix: "proxverse:cache"

  # Redis配置
  redis:
    # 是否启用Redis缓存
    enabled: true
    # 是否启用降级到Guava缓存
    fallback-to-guava: true
    # 默认过期时间（小时）
    default-expire-hours: 6
    # 连接超时时间（毫秒）
    timeout-ms: 3000
    # 最大重试次数
    max-retries: 3
    # 序列化类型：json, jdk, kryo
    serialization: json

  # Guava配置（作为降级备选）
  guava:
    # 最大缓存条目数
    maximum-size: 1000
    # 访问后过期时间
    expire-after-access: 6
    # 访问后过期时间单位
    expire-after-access-unit: HOURS
    # 写入后过期时间（-1表示不设置）
    expire-after-write: -1
    # 写入后过期时间单位
    expire-after-write-unit: HOURS
    # 初始容量
    initial-capacity: 16
    # 并发级别
    concurrency-level: 4
    # 是否启用软引用
    soft-values: false
    # 是否启用弱引用
    weak-values: false
    # 是否启用弱键
    weak-keys: false

# TopicFilter缓存配置 - 切换到Redis
proxverse:
  topic-filter:
    cache:
      # 缓存过期时间（小时）
      expire-hours: 6
      # 最大缓存条目数
      max-size: 100
      # Redis缓存键前缀
      redis-key-prefix: "proxverse:topic-filter"
      # 是否启用缓存降级策略（当Redis不可用时使用本地缓存）
      enable-fallback: true
      # Redis连接超时时间（毫秒）
      redis-timeout-ms: 3000
  satoken:
    cache:
      # Redis缓存键前缀
      redis-key-prefix: "proxverse:satoken"
      # 是否启用缓存降级策略（当Redis不可用时使用本地缓存）
      enable-fallback: true
      # Redis连接超时时间（毫秒）
      redis-timeout-ms: 3000

