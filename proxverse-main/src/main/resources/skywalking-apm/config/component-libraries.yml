# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Define all component libraries' names and IDs, used in monitored application.
# This is a bothway mapping, agent or SDK could use the value(ID) to represent the component name in uplink data.
#
# ######
#   id
# ######
# We highly recommend DO NOT change the IDs in these file, just append new one, and make sure the ID unique.
# Any replacement will cause visualization and aggregation error.
#
# All IDs in this files are reserved, even some IDs removed by some reasons, those IDs will be abandoned.
#
# ######
# languages
# ######
# Languages declare which languages are using this component. Multi languages should be separated by `,`

Unknown:
  id: 0
  language: All
Tomcat:
  id: 1
  languages: Java
HttpClient:
  id: 2
  languages: Java,C#,Node.js
Dubbo:
  id: 3
  languages: Java
H2:
  id: 4
  languages: Java
Mysql:
  id: 5
  languages: Java,C#,Node.js
ORACLE:
  id: 6
  languages: Java
Redis:
  id: 7
  languages: Java,C#,Node.js,PHP,Python
Motan:
  id: 8
  languages: Java
MongoDB:
  id: 9
  languages: Java,C#,Node.js,Python
Resin:
  id: 10
  languages: Java
Feign:
  id: 11
  languages: Java
OKHttp:
  id: 12
  languages: Java
SpringRestTemplate:
  id: 13
  languages: Java
SpringMVC:
  id: 14
  languages: Java
Struts2:
  id: 15
  languages: Java
NutzMVC:
  id: 16
  languages: Java
NutzHttp:
  id: 17
  languages: Java
JettyClient:
  id: 18
  languages: Java
JettyServer:
  id: 19
  languages: Java
Memcached:
  id: 20
  languages: Java,PHP
ShardingJDBC:
  id: 21
  languages: Java
PostgreSQL:
  id: 22
  languages: Java,C#,Node.js
GRPC:
  id: 23
  languages: Java,PHP
ElasticJob:
  id: 24
  languages: Java
RocketMQ:
  id: 25
  languages: Java
httpasyncclient:
  id: 26
  languages: Java
Kafka:
  id: 27
  languages: Java
ServiceComb:
  id: 28
  languages: Java
Hystrix:
  id: 29
  languages: Java
Jedis:
  id: 30
  languages: Java
SQLite:
  id: 31
  languages: Java,C#
h2-jdbc-driver:
  id: 32
  languages: Java
mysql-connector-java:
  id: 33
  languages: Java
ojdbc:
  id: 34
  languages: Java
Spymemcached:
  id: 35
  languages: Java
Xmemcached:
  id: 36
  languages: Java
postgresql-jdbc-driver:
  id: 37
  languages: Java
rocketMQ-producer:
  id: 38
  languages: Java
rocketMQ-consumer:
  id: 39
  languages: Java
kafka-producer:
  id: 40
  languages: Java,Python
kafka-consumer:
  id: 41
  languages: Java,Python
mongodb-driver:
  id: 42
  languages: Java
SOFARPC:
  id: 43
  languages: Java
ActiveMQ:
  id: 44
  languages: Java
activemq-producer:
  id: 45
  languages: Java
activemq-consumer:
  id: 46
  languages: Java
Elasticsearch:
  id: 47
  languages: Java,Python
transport-client:
  id: 48
  languages: Java
http:
  id: 49
  languages: Java,C#,Node.js
rpc:
  id: 50
  languages: Java,C#,Node.js
RabbitMQ:
  id: 51
  languages: Java
rabbitmq-producer:
  id: 52
  languages: Java,Python,PHP
rabbitmq-consumer:
  id: 53
  languages: Java,Python,PHP
Canal:
  id: 54
  languages: Java
Gson:
  id: 55
  languages: Java
Redisson:
  id: 56
  languages: Java
Lettuce:
  id: 57
  languages: Java
Zookeeper:
  id: 58
  languages: Java
Vertx:
  id: 59
  languages: Java
ShardingSphere:
  id: 60
  languages: Java
spring-cloud-gateway:
  id: 61
  languages: Java
RESTEasy:
  id: 62
  languages: Java
SolrJ:
  id: 63
  languages: Java
Solr:
  id: 64
  languages: Java
SpringAsync:
  id: 65
  languages: Java
JdkHttp:
  id: 66
  languages: Java
spring-webflux:
  id: 67
  languages: Java
Play:
  id: 68
  languages: Java,Scala
cassandra-java-driver:
  id: 69
  languages: Java
Cassandra:
  id: 70
  languages: Java
Light4J:
  id: 71
  languages: Java
Pulsar:
  id: 72
  languages: Java
pulsar-producer:
  id: 73
  languages: Java
pulsar-consumer:
  id: 74
  languages: Java
Ehcache:
  id: 75
  languages: Java
SocketIO:
  id: 76
  languages: Java
rest-high-level-client:
  id: 77
  languages: Java
spring-tx:
  id: 78
  languages: Java
Armeria:
  id: 79
  languages: Java
JdkThreading:
  id: 80
  languages: Java
KotlinCoroutine:
  id: 81
  languages: Java
AvroServer:
  id: 82
  languages: Java
AvroClient:
  id: 83
  languages: Java
Undertow:
  id: 84
  languages: Java
Finagle:
  id: 85
  languages: Java,Scala
Mariadb:
  id: 86
  languages: Java
mariadb-jdbc:
  id: 87
  languages: Java
quasar:
  id: 88
  languages: Java
InfluxDB:
  id: 89
  languages: Java
influxdb-java:
  id: 90
  languages: Java
brpc-java:
  id: 91
  languages: Java
GraphQL:
  id: 92
  languages: Java
spring-annotation:
  id: 93
  languages: Java
HBase:
  id: 94
  languages: Java
spring-kafka-consumer:
  id: 95
  languages: Java
SpringScheduled:
  id: 96
  languages: Java
quartz-scheduler:
  id: 97
  languages: Java
xxl-job:
  id: 98
  languages: Java
spring-webflux-webclient:
  id: 99
  languages: Java
thrift-server:
  id: 100
  languages: Java
thrift-client:
  id: 101
  languages: Java
AsyncHttpClient:
  id: 102
  languages: Java
dbcp:
  id: 103
  languages: Java
mssql-jdbc-driver:
  id: 104
  languages: Java
Apache-CXF:
  id: 105
  languages: Java

# .NET/.NET Core components
# [3000, 4000) for C#/.NET only
AspNetCore:
  id: 3001
  languages: C#
EntityFrameworkCore:
  id: 3002
  languages: C#
SqlClient:
  id: 3003
  languages: C#
CAP:
  id: 3004
  languages: C#
StackExchange.Redis:
  id: 3005
  languages: C#
SqlServer:
  id: 3006
  languages: C#,Java
Npgsql:
  id: 3007
  languages: C#
MySqlConnector:
  id: 3008
  languages: C#
EntityFrameworkCore.InMemory:
  id: 3009
  languages: C#
EntityFrameworkCore.SqlServer:
  id: 3010
  languages: C#
EntityFrameworkCore.Sqlite:
  id: 3011
  languages: C#
Pomelo.EntityFrameworkCore.MySql:
  id: 3012
  languages: C#
Npgsql.EntityFrameworkCore.PostgreSQL:
  id: 3013
  languages: C#
InMemoryDatabase:
  id: 3014
  languages: C#
AspNet:
  id: 3015
  languages: C#
SmartSql:
  id: 3016
  languages: C#

# NoeJS components
# [4000, 5000) for Node.js agent
HttpServer:
  id: 4001
  languages: Node.js
express:
  id: 4002
  languages: Node.js
Egg:
  id: 4003
  languages: Node.js
Koa:
  id: 4004
  languages: Node.js

# Golang components
# [5000, 6000) for Golang agent
ServiceCombMesher:
  id: 5001
  languages: Golang
ServiceCombServiceCenter:
  id: 5002
  languages: Golang
MOSN:
  id: 5003
  languages: Golang
GoHttpServer:
  id: 5004
  languages: Golang
GoHttpClient:
  id: 5005
  languages: Golang
Gin:
  id: 5006
  languages: Golang
Gear:
  id: 5007
  languages: Golang
GoMicroClient:
  id: 5008
  languages: Golang
GoMicroServer:
  id: 5009
  languages: Golang

# Lua components
# [6000, 7000) for Lua agent
Nginx:
  id: 6000
  languages: Lua


# [7000, 8000) are reserved for Python components
Python:
  id: 7000
  languages: Python
Flask:
  id: 7001
  languages: Python
Requests:
  id: 7002
  languages: Python
PyMysql:
  id: 7003
  languages: Python
Django:
  id: 7004
  languages: Python
Tornado:
  id: 7005
  languages: Python
Urllib3:
  id: 7006
  languages: Python
Sanic:
  id: 7007
  languages: Python

# PHP components
# [8000, 9000) for PHP agent
PHP:
  id: 8001
  languages: PHP
cURL:
  id: 8002
  languages: PHP
PDO:
  id: 8003
  languages: PHP
Mysqli:
  id: 8004
  languages: PHP
Yar:
  id: 8005
  languages: PHP
Predis:
  id: 8006
  languages: PHP

# C++ components
# [9000, 10000) for C++ agent
EnvoyProxy:
  id: 9000
  languages: C++

# Component Server mapping defines the server display names of some components
# e.g.
# Jedis is a client library in Java for Redis server
Component-Server-Mappings:
  mongodb-driver: MongoDB
  rocketMQ-producer: RocketMQ
  rocketMQ-consumer: RocketMQ
  kafka-producer: Kafka
  kafka-consumer: Kafka
  activemq-producer: ActiveMQ
  activemq-consumer: ActiveMQ
  rabbitmq-producer: RabbitMQ
  rabbitmq-consumer: RabbitMQ
  postgresql-jdbc-driver: PostgreSQL
  Xmemcached: Memcached
  Spymemcached: Memcached
  h2-jdbc-driver: H2
  mysql-connector-java: Mysql
  Jedis: Redis
  StackExchange.Redis: Redis
  Redisson: Redis
  Lettuce: Redis
  Zookeeper: Zookeeper
  SqlClient: SqlServer
  Npgsql: PostgreSQL
  MySqlConnector: Mysql
  EntityFrameworkCore.InMemory: InMemoryDatabase
  EntityFrameworkCore.SqlServer: SqlServer
  EntityFrameworkCore.Sqlite: SQLite
  Pomelo.EntityFrameworkCore.MySql: Mysql
  Npgsql.EntityFrameworkCore.PostgreSQL: PostgreSQL
  transport-client: Elasticsearch
  SolrJ: Solr
  cassandra-java-driver: Cassandra
  pulsar-producer: Pulsar
  pulsar-consumer: Pulsar
  rest-high-level-client: Elasticsearch
  mariadb-jdbc: Mariadb
  Mysqli: Mysql
  influxdb-java: InfluxDB
  Predis: Redis
  PyMysql: Mysql
  spring-kafka-consumer: kafka-consumer
  mssql-jdbc-driver: SqlServer
