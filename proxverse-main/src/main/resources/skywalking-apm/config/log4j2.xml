<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<Configuration status="info">
    <Properties>
        <Property name="log-path">${sys:oap.logDir}</Property>
    </Properties>
    <Appenders>
        <RollingFile name="RollingFile" fileName="${log-path}/skywalking-oap-server.log"
                     filePattern="${log-path}/skywalking-oap-server-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout>
                <pattern>%d - %c - %L [%t] %-5p %x - %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="102400KB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>
    <Loggers>
        <logger name="org.eclipse.jetty" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="io.grpc.netty" level="INFO"/>
        <Root level="info">
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration>
