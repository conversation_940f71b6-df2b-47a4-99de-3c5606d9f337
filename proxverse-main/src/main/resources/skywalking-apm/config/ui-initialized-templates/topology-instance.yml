# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# UI templates initialized file includes the default template when the SkyWalking OAP starts up at the first time.
#
# Also, SkyWalking would detect the existing templates in the database, once they are missing, all templates in this file
# could be added automatically.

templates:
  - name: "Topology Instance"
    # The type includes DASHBOARD, TOPOLOGY_INSTANCE, TOPOLOGY_ENDPOINT.
    # DASHBOARD type templates could have multiple definitions, by using different names.
    # TOPOLOGY_INSTANCE, TOPOLOGY_ENDPOINT type templates should be defined once, as they are used in the topology page only.
    type: "TOPOLOGY_INSTANCE"
    # Configuration could be defined through UI, and use `export` to format in the standard JSON.
    configuration: |-
      [
        {
          "width": "4",
          "title": "Service Instance Load",
          "height": "150",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "service_instance_cpm",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "CPM - calls per minute"
        },
        {
          "width": "4",
          "title": "Service Instance Successful Rate",
          "height": "150",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "service_instance_resp_time",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "%",
          "aggregation": "/",
          "aggregationNum": "100"
        },
        {
          "width": "4",
          "title": "Service Instance Latency",
          "height": "150",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "service_instance_resp_time",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "ms"
        },
        {
          "width": 3,
          "title": "JVM CPU (Java Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_jvm_cpu",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "%",
          "aggregation": "+",
          "aggregationNum": ""
        },
        {
          "width": 3,
          "title": "JVM Memory (Java Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_jvm_memory_heap, instance_jvm_memory_heap_max,instance_jvm_memory_noheap, instance_jvm_memory_noheap_max",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "MB",
          "aggregation": "/",
          "aggregationNum": "1048576"
        },
        {
          "width": 3,
          "title": "JVM GC Time",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_jvm_young_gc_time, instance_jvm_old_gc_time",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "ms"
        },
        {
          "width": 3,
          "title": "JVM GC Count",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartBar",
          "metricName": "instance_jvm_young_gc_count, instance_jvm_old_gc_count"
        },
        {
          "width": 3,
          "title": "CLR CPU  (.NET Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_clr_cpu",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "%"
        },
        {
          "width": 3,
          "title": "CLR GC (.NET Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_clr_gen0_collect_count, instance_clr_gen1_collect_count, instance_clr_gen2_collect_count",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartBar"
        },
        {
          "width": 3,
          "title": "CLR Heap Memory (.NET Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "metricName": "instance_clr_heap_memory",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "unit": "MB"
        },
        {
          "width": 3,
          "title": "CLR Thread (.NET Service)",
          "height": "250",
          "entityType": "ServiceInstance",
          "independentSelector": false,
          "metricType": "REGULAR_VALUE",
          "queryMetricType": "readMetricsValues",
          "chartType": "ChartLine",
          "metricName": "instance_clr_available_completion_port_threads,instance_clr_available_worker_threads,instance_clr_max_completion_port_threads,instance_clr_max_worker_threads"
        }
      ]
    # Activated as the TOPOLOGY_INSTANCE type, makes this templates added into the UI page automatically.
    # False means providing a basic template, user needs to add it manually.
    activated: true
    # True means wouldn't show up on the dashboard. Only keeps the definition in the storage.
    disabled: false