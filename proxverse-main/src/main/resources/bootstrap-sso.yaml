spring:
    application:
        name: pm-project
    cloud:
        nacos:
            discovery:
                enabled: false
            config:
                enabled: false


    datasource:
        dynamic:
            # 设置默认的数据源或数据源组，默认值即为master
            primary: engine
            # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
            strict: false
            datasource:
                oauth2:
                    username: root
                    password: xhy123456
                    url: ******************************************************************************************
                    driver-class-name: com.mysql.jdbc.Driver
                    type: com.alibaba.druid.pool.DruidDataSource
                engine:
                    username: root
                    password: xhy123456
                    url: *******************************************************************************************
                    driver-class-name: com.mysql.jdbc.Driver
                    type: com.alibaba.druid.pool.DruidDataSource
                execution:
                    username: root
                    password: xhy123456
                    url: *********************************************************************************************
                    driver-class-name: com.mysql.jdbc.Driver
                    type: com.alibaba.druid.pool.DruidDataSource
    servlet:
        multipart:
            max-file-size: 20981970
            max-request-size: 20981970
    security:
        oauth2:
            client:
                provider:
                    okta:
                        authorization-uri: https://dev-50350041.okta.com/oauth2/v1/authorize
                        token-uri: https://dev-50350041.okta.com/oauth2/v1/token
                        user-info-uri: https://dev-50350041.okta.com/oauth2/v1/userinfo
                        user-name-attribute: preferred_username
                        jwk-set-uri: https://dev-50350041.okta.com/oauth2/v1/keys

                    demo:
                        authorization-uri: authorize
                        token-uri: token
                        user-info-uri: userinfo
                        user-name-attribute: name
                        jwk-set-uri: keys

                    prx:
                        #  获取访问令牌
                        token-uri: http://localhost:8887/oauth/token
                        authorization-uri: http://localhost:8887/oauth/authorize
                        user-info-uri: http://localhost:8887/oauth/userinfo
                        user-name-attribute: username


                registration:

                    prx:
                        client-id: oidcOauth
                        client-secret: secret
                        authorization-grant-type: authorization_code
                        client-name: proxverse_oauth
                        scope: "login"
                        redirect-uri: "http://localhost:8888/login/oauth2/code/prx"

                    demo:
                        client-id: clientId
                        client-secret: secret
                        authorization-grant-type: authorization_code
                        redirect-uri: redirectURL
                        scope: "openid, profile, email, address, phone, offline_access"
                        client-name: demo

                    okta:
                        client-id: 0oa8jp0qmnMGgQhFW5d7
                        client-secret: yH6jC0lm-9XKa3-KZSTwAvdNO7kIB8JyyQwVg3Bj
                        authorization-grant-type: authorization_code
                        redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
                        scope: "openid, profile, email, address, phone, offline_access"
                        client-name: okta



server:
    port: 8888
    sleuth:
        log:
            slf4j:
                enabled: true
        sampler:
            # 抽样率，默认0.1
            probability: 1.0


    servlet:
        multipart:
            enabled: true
            max-file-size: 2000MB
            max-request-size: 2000MB



oauth2.license.path: /Users/<USER>/process/filePath/license
logging:
    level:
        com.sp.spengine.mapper: debug
        root: info
        org.springframework.web.servlet.DispatcherServlet: DEBUG
        org.springframework.cloud.sleuth: DEBUG
        com.baomidou.mybatisplus: DEBUG


csv.file.save.path: /Users/<USER>/process/filePath/tmp/upload/
csv.child.file.save.path: /Users/<USER>/process/filePath/tmp/upload/child/
del.file.path: /Users/<USER>/process/filePath/tmp

spring.cache.enabled: false
spark.store.path: /Users/<USER>/process/filePath/tmp/warehouse/
spark.metastore.jdbc.url: **************************************************************************
spark.metastore.jdbc.user: root
spark.metastore.jdbc.password: xhy123456
spark.master: local[16]


redis.env: dev

spring.redis.host: *************
spring.redis.port: 6379
#spring.redis.password: qfyp@2021
#spring.redis.database: 2
#连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active: 50
#连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait: 3000
#连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle: 20
#连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle: 2
#连接超时时间（毫秒）
spring.redis.timeout: 5000

security:
    oauth2:
        client:
            client-id: client
            client-secret: secret
            access-token-uri: http://localhost:8888/oauth/token
            user-authorization-uri: http://localhost:8888/oauth/authorize
        resource:
            token-info-uri: http://localhost:8888/oauth/check_token
mybatis-plus:
    mapper-locations: classpath:mapper/**.xml


spark:
    sql:
        columnar:
            enabled: false



# sso配置
sso:
    # 授权服务类型，已经支持的有：OKTA
    authorityClientEnum: OKTA
    key: ******************************************
    baseUrl: https://dev-50350041.okta.com
    name: OKTA平台
    clientId: 0oa8jp0qmnMGgQhFW5d7
    logout: https://dev-50350041.okta.com/login/signout
    clientSecret: yH6jC0lm-9XKa3-KZSTwAvdNO7kIB8JyyQwVg3Bj





# 独立部署情况下是否需要groupId
prx.login.sso.enabled: true
sso.group.id: 1
front.end: http://*************:8889
cookie.domain: dev-50350041.okta.com

prx.admin.license.enabled: false


spark.conf: "{\"spark.sql.legacy.timeParserPolicy\": \"LEGACY\",\"spark.sql.parquet.datetimeRebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInRead\": \"LEGACY\"}"


system.timezone: Asia/Shanghai

