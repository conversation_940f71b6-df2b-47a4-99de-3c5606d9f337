spring:
  application:
    name: pm-project
  # flyway
  flyway:
    enabled: true
    # sql编码
    encoding: utf-8
    # 版本文件路径
    locations: classpath:ddl/mysql/migration
    validate-on-migrate: true
    # 是否以当前数据为基线
    baselineOnMigrate: true
    # 当前数据为基线的时候版本号
    baselineVersion: 1.0.0
    # 是否允许执行比当前版本号低的版本sql
    outOfOrder: true
  database:
    auto-init: true
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

  servlet:
    multipart:
      enabled: true
      max-file-size: 2000MB
      max-request-size: 2000MB


  datasource:
    dynamic:
      # 设置默认的数据源或数据源组，默认值即为master
      primary: engine
      # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      datasource:
        oauth2:
          username: root
          password: snfnqi123
          url: ********************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        engine:
          username: root
          password: snfnqi123
          url: *********************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        execution:
          username: root
          password: snfnqi123
          url: ***********************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never


server:
  port: 8888
  sleuth:
    log:
      slf4j:
        enabled: true
    sampler:
      # 抽样率，默认0.1
      probability: 1.0




oauth2.license.path: C:\Users\<USER>\Desktop\licfile
logging:
  level:
    com.sp.proxverse.common.mapper: info
    root: info
    org.springframework.web.servlet.DispatcherServlet: error
    org.springframework.cloud.sleuth: info
    org.apache.spark: error

csv.file.save.path: C:/Users/<USER>/Desktop/xesfile/
del.file.path: C:/Users/<USER>/Desktop/xesfile/
export.path: C:/Users/<USER>/Desktop/xesfile/

spring.cache.enabled: false
spark.store.path: C:/Users/<USER>/Desktop/sparkfile/
spark.metastore.jdbc.url: ********************************************************************************************************************
spark.metastore.jdbc.user: root
spark.metastore.jdbc.password: snfnqi123
spark.master: local[16]


redis.env: dev

spring.redis.host: *************
spring.redis.port: 30379
#连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active: 50
#连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait: 3000
#连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle: 20
#连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle: 2
#连接超时时间（毫秒）
spring.redis.timeout: 5000

security:
  oauth2:
    client:
      client-id: client
      client-secret: secret
      access-token-uri: http://localhost:8888/oauth/token
      user-authorization-uri: http://localhost:8888/oauth/authorize
    resource:
      token-info-uri: http://localhost:8888/oauth/check_token
mybatis-plus:
  mapper-locations: classpath:mapper/**.xml



spark.sql.columnar.enabled: false
spark.conf: "{\"spark.sql.legacy.timeParserPolicy\": \"LEGACY\",\"spark.sql.parquet.datetimeRebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInRead\": \"LEGACY\"}"


# 这里，jdbcurl链接上加上时区，mysql容器加上-e TZ=Asia/Shanghai，则可确保时区不会出错，而且与宿主机的时区没有关系
system.timezone: UTC



############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  log-Level: debug
  # 每次清理过期数据间隔的时间 （单位: 秒） ，默认值30秒，设置为-1代表不启动定时清理
  data-refresh-period: 3600
  # 自定义配置: 是否使用mySql存储session信息
  enable-mysql-memory: true

#默认密码
prx.admin.local.initPassword: proxverse
#快速开始的csv文件默认发票文件
prx.dataModel.file.quickStartDefaultFile: proxverse_demo
# 上传文件的限制大小默认1GB
prx.dataModel.file.maxUploadSize: 1g
#快速开始的限制大小默认50MB
prx.dataModel.file.quickStartMaxUploadSize: 50m
#流程图显示的最大event默认500
prx.workbench.processTree.maxQueryEvents: 500
#流程图显示的最大connect默认500
prx.workbench.processTree.maxQueryConnects: 500
#日志保存的默认天数默认14天
prx.gateway.log.keepDays: 14
#echarts查询top数量
prx.workbench.component.echartsQueryTopCount: 1000
prx.admin.license.enabled: true
prx.workbench.sheet.newsheetAiEnabled: true
prx.workbench.conformance.bpmnConditionExpressionEnabled: true
