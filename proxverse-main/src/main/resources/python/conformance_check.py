import pm4py
import sys
import pandas as pd

if __name__ == '__main__':
    RESOURCES_PATH = sys.argv[1]
    bpmn_path = RESOURCES_PATH + sys.argv[2]
    bpmn_graph = pm4py.read_bpmn(bpmn_path)
    net, im, fm = pm4py.convert_to_petri_net(bpmn_graph)

    event_log_path = RESOURCES_PATH + 'eventLog.txt'
    dic = {}
    case_ids = []
    acts = []
    with open(event_log_path, 'r') as f:
        rows = f.readlines()
        for row in rows:
            case_id, act = row.split(',')[0], row.split(',')[1]
            case_ids.append(case_id)
            acts.append(act)
        dic['case:concept:name'] = case_ids
        dic['concept:name'] = acts

    df = pd.DataFrame(dic)
    log = pm4py.convert_to_event_log(df)

    checks = pm4py.conformance_diagnostics_token_based_replay(log, net, im, fm)
    with open(RESOURCES_PATH + 'trace_is_fit.txt', 'w') as f:
        for check in checks:
            f.write(str(check['trace_is_fit']) + '\n')

