import pm4py
import sys
if __name__ == '__main__':
    RESOURCES_PATH = sys.argv[1]
    Bpmn_file_name = sys.argv[2]
    dfg = {}
    start_act = []
    end_act = []
    mode = 0
    print('test py')
    with open(RESOURCES_PATH + 'variants.txt', 'r') as f:
        rows = f.readlines()
        for row in rows:
            if '=' in row:
                mode = 1
                continue
            if '+' in row:
                mode = 2
                continue

            if mode == 0:
                key, value = row.split(':')[0], int(row.split(':')[1][0:1])
                source, target = key.split(',')[0], key.split(',')[1]
                edge = (source, target)
                dfg[edge] = value
            elif mode == 1:
                start_act.append(row[:-1])
            elif mode == 2:
                end_act.append(row[:-1])
        bpmn = pm4py.convert_to_bpmn(dfg, start_act, end_act)
        pm4py.write_bpmn(bpmn, RESOURCES_PATH + Bpmn_file_name)