<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationSignalDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationProjectListBO" id="optimizationSignalMap">
        <result property="id" column="Id"/>
        <result property="name" column="name"/>
        <result property="createrId" column="creater_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="dataModelId" column="data_model_id"/>
    </resultMap>

    <resultMap type="com.sp.proxverse.common.model.po.OptimizationSignalPO" id="optimizationSignalPOMap">
        <result property="id" column="Id"/>
        <result property="optimizationObjectId" column="optimization_object_id"/>
        <result property="createrId" column="creater_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="listByIds4Tenant" resultMap="optimizationSignalPOMap" >
        select Id,optimization_object_id,creater_id,update_time,create_time,tenant_id,deleted
        from t_optimization_signal where Id in
        <foreach collection="ids" item="objectId" open="(" close=")" separator=",">
            #{objectId}
        </foreach>
    </select>

    <select id="getExecuteOptimizationObjects" parameterType="integer" resultMap="optimizationSignalMap">
        SELECT
            o.name,
            o.id,
            o.creater_id,
            o.`status`,
            o.update_time,
            tc.data_model_id
        FROM
            t_optimization_object AS o
                LEFT JOIN t_optimization_signal AS s
                          ON o.id = s.`optimization_object_id`
                LEFT JOIN t_optimization_signal_data_model_table_column AS tc
                          ON s.Id = tc.optimization_signal_id
        WHERE o.`optimization_project_id` = #{projectId} AND o.`deleted` = 0 GROUP BY o.name, o.id, o.creater_id, o.`status`, o.update_time, tc.data_model_id ORDER BY o.update_time DESC
    </select>

</mapper>