<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationExecutionManagementRulesDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationExecutionManagementRulesPO" id="optimizationExecutionManagementRulesMap">
        <result property="id" column="Id"/>
        <result property="optimizationExecutionId" column="optimization_execution_id"/>
        <result property="optimizationExecutionDataColumnId" column="optimization_execution_data_column_id"/>
        <result property="fieldTypeRuleId" column="field_type_rule_id"/>
        <result property="matchValue" column="match_value"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>