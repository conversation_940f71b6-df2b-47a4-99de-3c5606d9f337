<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationObjectDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationObjectPO" id="optimizationObjectMap">
        <result property="id" column="Id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="optimizationProjectId" column="optimization_project_id"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="listByIds4Tenant" resultMap="optimizationObjectMap" >
        select Id,name,status,description,optimization_project_id,creater_id,create_time,update_time,deleted,tenant_id
        from t_optimization_object where Id in
        <foreach collection="ids" item="objectId" open="(" close=")" separator=",">
            #{objectId}
        </foreach>
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.ExecutionOptimizationListBO" id="executionOptimizationListMap">
        <result property="docount" column="docount"/>
        <result property="mainCount" column="mainCount"/>
        <result property="id" column="Id"/>
        <result property="name" column="name"/>
    </resultMap>

    <select id="getExecutionOptimizationList" resultMap="executionOptimizationListMap" >
        SELECT
            COUNT(t.`id`) AS mainCount,
            COUNT(IF(t.status = 'UNLOCK', 0, NULL)) AS nodone,
            o.`Id`,
            o.`name`
        FROM
            `t_optimization_object` AS o
                LEFT JOIN `t_optimization_task` AS t
                          ON t.`optimization_object_id` = o.`Id`
        WHERE o.`deleted` = 0
          AND t.`deleted` = 0
          AND o.`Id` in
        <foreach collection="objectIds" item="objectId" open="(" close=")" separator=",">
            #{objectId}
        </foreach>
        GROUP BY o.`Id`
    </select>

    <resultMap type="com.sp.proxverse.common.model.po.AllExecutionOptimizationListPO" id="allExecutionOptimizationListMap">
        <result property="id" column="Id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="assignedUserId" column="assigned_user_id"/>
    </resultMap>

    <select id="getAllExecutionOptimizationList" resultMap="allExecutionOptimizationListMap" >
        SELECT
            `t_optimization_object`.`Id`,
            `t_optimization_object`.`name`,
            `t_optimization_task`.`assigned_user_id`
        FROM
            `t_optimization_task`,
            `t_optimization_signal`,
            `t_optimization_object`
        WHERE `t_optimization_task`.`optimization_signal_id` = `t_optimization_signal`.`Id`
          AND `t_optimization_signal`.`optimization_object_id` = `t_optimization_object`.`Id`
          AND `t_optimization_task`.`deleted` = 0
          AND `t_optimization_signal`.`deleted` = 0
          AND `t_optimization_object`.`deleted` = 0
          AND `t_optimization_task`.`assigned_user_id` = #{userId} ORDER BY `t_optimization_object`.create_time,`t_optimization_signal`.create_time,`t_optimization_task`.create_time DESC
    </select>

    <select id="getObjectDetailById" resultMap="objectMap" >
        SELECT `id`,`name`,`status` from t_optimization_object  where `deleted` = 0 AND `id` = #{id}
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationObjectBO" id="objectMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <association property="signal" column="id" select="signalById"/>
        <association property="rules" column="id" select="rulesById"/>
    </resultMap>

    <select id="signalById" resultMap="signalMap">
        SELECT `id` from  t_optimization_signal where `deleted` = 0 and optimization_object_id = #{id}
    </select>


    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationSignalBO" id="signalMap">
        <result property="id" column="id"/>
        <collection property="tableColumnList" ofType="com.sp.proxverse.common.model.bo.SignalDataModelTableColumnBO" select="getColumnBysignalId" column="id" />
    </resultMap>

    <select id="getColumnBysignalId" resultMap="signalTableColumnMap">
        SELECT `id`,`column_id`,`data_model_id` from  t_optimization_signal_data_model_table_column where `deleted` = 0 and optimization_signal_id = #{id}
    </select>

    <resultMap type="com.sp.proxverse.common.model.bo.SignalDataModelTableColumnBO" id="signalTableColumnMap">
        <result property="id" column="id"/>
        <result property="dataModelId" column="data_model_id"/>
        <result property="columnId" column="column_id"/>
    </resultMap>

    <select id="rulesById" resultMap="rulesMap">
        SELECT `id`,`type` from  t_optimization_execution_rules where `deleted` = 0 and optimization_object_id = #{id}
    </select>


    <resultMap type="com.sp.proxverse.common.model.bo.OptimizationExecutionRulesBO" id="rulesMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
    </resultMap>



</mapper>