<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationSignalDataModelTableColumnDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationSignalDataModelTableColumnPO" id="optimizationSignalDataModelTableColumnMap">
        <result property="id" column="Id"/>
        <result property="dataModelId" column="data_model_id"/>
        <result property="fileId" column="file_id"/>
        <result property="columnId" column="column_id"/>
        <result property="optimizationSignalId" column="optimization_signal_id"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>