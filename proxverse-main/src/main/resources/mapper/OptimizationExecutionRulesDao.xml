<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationExecutionRulesDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationExecutionRulesPO" id="optimizationExecutionRulesMap">
        <result property="id" column="Id"/>
        <result property="optimizationObjectId" column="optimization_object_id"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>