<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sp.proxverse.execution.dao.OptimizationTaskLogDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sp.proxverse.common.model.po.OptimizationTaskLogPO" id="optimizationTaskLogMap">
        <result property="id" column="Id"/>
        <result property="optimizationTaskId" column="optimization_task_id"/>
        <result property="distributionUserId" column="distribution_user_id"/>
        <result property="assignedUserId" column="assigned_user_id"/>
        <result property="status" column="status"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>