<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.TopicSheetConformanceMapper">

    <!--
        <select id="getTopicSheetCauseList" resultType="com.sp.spengine.model.dto.topicSheet.TopicSheetCauseDto">


            select
                   ttsc.id sheetCauseId,
                   tck.id rootCauseId,
                   tck.id causeName,
                   tck.id sortRule,

            from t_topic_sheet_cause ttsc
                     join t_root_cause tck on ttsc.cause_id = tck.id
            where ttsc.topic_sheet_id = #{topicSheetId} ttsc.deleted = 0
            order by ttsc.id desc
        </select>-->

    <select id="getSheetConformanceByTopicSheetId"
            resultType="com.sp.proxverse.common.model.dto.SheetConformance.SheetConformanceDto">

        select ttsc.id        sheetConformanceId,
               tfv.id         fileVariantId,
               tfv.file_id    fileId,
               tfv.variant    variant,
               tfv.count      variantCount,
               tfv.type       variantType,
               tfv.variant_id variantId,
               tfv.variant_id dataModelId
        from t_topic_sheet_conformance ttsc
                 join t_file_variant tfv
                      on ttsc.file_variant_id = tfv.id
        where ttsc.topic_sheet_id = #{topicSheetId}
          and ttsc.deleted = 0
        order by ttsc.id desc;
    </select>
</mapper>