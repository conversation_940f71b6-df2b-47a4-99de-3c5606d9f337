<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.ProcessManageMapper">

    <select id="getProcessManageDatumLibrary" resultType="com.sp.proxverse.common.model.po.ProcessManagePo">
        SELECT *
        FROM t_process_manage
        WHERE
        release_flag = 0
        AND deleted = 0
        AND datum_library_flag = 0
        AND
        id IN
        <foreach item="item" collection="datumLibraryProcessIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY level DESC, id ASC
    </select>

</mapper>
