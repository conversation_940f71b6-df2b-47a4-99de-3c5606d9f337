<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.UserDataAuthorityMapper">

    <sql id="sql_list_dataAuthority">
        from t_user_data_authority tuda
        join t_data_authority tda
        on tuda.data_authority_id = tda.id
        join t_admin ta on ta.id = tuda.user_id
        where tuda.deleted = 0
            AND ta.status = 1
        <if test="dataAuthorityId != null ">
            AND tda.id = #{dataAuthorityId}
        </if>
        <if test="userId != null ">
            AND tuda.user_id = #{userId}
        </if>
    </sql>

    <select id="selectListDataAuthority" resultType="com.sp.proxverse.common.model.dto.UserDataAuthorityInfoDto">
        select tuda.authority_value,
        tuda.id,
        tuda.user_id,
        ta.user_name,
        tda.data_id,
        ta.level userLevel,
        tda.data_type
        <include refid="sql_list_dataAuthority" />

    </select>
    <select id="selectListDataAuthorityPage" resultType="com.sp.proxverse.common.model.dto.UserDataAuthorityInfoDto">
        select tuda.authority_value,
        tuda.id,
        tuda.user_id,
        ta.user_name,
        tda.data_id,
        tda.data_type
        sql_list_dataAuthority

        <include refid="sql_list_dataAuthority" />
        <if test="startNum != null and pageSize != null ">
            limit ${startNum},${pageSize}
        </if>
    </select>

    <select id="selectListDataAuthorityCount" resultType="Long">
        select count(*)

        <include refid="sql_list_dataAuthority" />
    </select>

    <select id="getUserTopicFilter" resultType="String">
        select tuda.filter
        from t_user_data_authority tuda
                 left join t_data_authority tda
            on tuda.data_authority_id = tda.id
        where tuda.user_id=#{userId} and tda.data_id=#{dataId} and tda.data_type=1 limit 1
    </select>

  <select id="selectUserPage" resultType="com.sp.proxverse.common.model.dto.dataauth.UserDataAuthDTO">
    SELECT DISTINCT id, user_name userName, `name` name, 10 idType, level
    from
    (
    SELECT DISTINCT ug.id, ug.user_name, `name`, level, uda.authority_value, uda.data_authority_id, ug.create_time
    from t_admin ug
    left join t_user_data_authority uda on ug.id=uda.user_id and uda.data_authority_id = #{authorityId} and uda.deleted=0
    where
    ug.status in
      <foreach item="id" collection="states" open="(" separator="," close=")">
          #{id}
      </foreach>
    and ug.deleted = 0
    <if test="id == null ">
      and (ug.level != 'ROOT' and ug.level != 'SUPER' or ug.level is null)
    </if>
    <if test="name != null ">
      and (ug.user_name like concat('%', #{name}, '%') or ug.`name` like concat('%', #{name}, '%'))
    </if>
    <if test="id != null ">
      and ug.id = #{id}
    </if>
    order by uda.data_authority_id desc, uda.authority_value desc, ug.create_time desc
    ) t
    <if test="startNum != null ">
      limit #{startNum}, #{pageSize}
    </if>
  </select>

  <select id="selectUserCount" resultType="integer">
    SELECT count(*) from t_admin
    where
    status = 1
    and deleted = 0
    and (level != 'ROOT' AND level != 'SUPER' OR level is null)
    <if test="name != null ">
      and (user_name like concat('%', #{name}, '%') or `name` like concat('%', #{name}, '%'))
    </if>
  </select>

  <select id="selectGroupPage" resultType="com.sp.proxverse.common.model.dto.dataauth.UserDataAuthDTO">
    SELECT DISTINCT id, name, 20 idType
    from
    (
    SELECT DISTINCT ug.id, ug.name, uda.authority_value, uda.data_authority_id, ug.create_time
    from t_user_group ug
    left join t_user_data_authority uda on ug.id=uda.user_group_id and uda.data_authority_id = #{authorityId} and
    uda.deleted = 0
    where
    ug.deleted = 0
    <if test="name != null ">
      and ug.name like concat('%', #{name}, '%')
    </if>
    order by uda.data_authority_id desc, uda.authority_value desc, ug.create_time desc
    ) t
    <if test="startNum != null ">
      limit #{startNum}, #{pageSize}
    </if>
  </select>

  <select id="selectGroupCount" resultType="integer">
    SELECT count(*) from t_user_group
    where deleted = 0
    <if test="name != null ">
      and name like concat('%', #{name}, '%')
    </if>
  </select>
</mapper>
