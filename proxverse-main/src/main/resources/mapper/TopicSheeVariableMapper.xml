<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sp.proxverse.common.mapper.TopicSheetVariableMapper">

<!--
    <select id="getTopicSheetCauseList" resultType="com.sp.spengine.model.dto.topicSheet.TopicSheetCauseDto">


        select
               ttsc.id sheetCauseId,
               tck.id rootCauseId,
               tck.id causeName,
               tck.id sortRule,

        from t_topic_sheet_cause ttsc
                 join t_root_cause tck on ttsc.cause_id = tck.id
        where ttsc.topic_sheet_id = #{topicSheetId} ttsc.deleted = 0
        order by ttsc.id desc
    </select>-->
</mapper>