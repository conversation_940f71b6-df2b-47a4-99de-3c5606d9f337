spring:
  application:
    name: pm-project
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false


  datasource:
    dynamic:
      # 设置默认的数据源或数据源组，默认值即为master
      primary: engine
      # 严格匹配数据源，默认false，true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      datasource:
        oauth2:
          username: root
          password: 123456
          url: ******************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        engine:
          username: root
          password: 123456
          url: *******************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        execution:
          username: root
          password: 123456
          url: *********************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        meta:
          username: root
          password: 123456
          url: *************************************************************************************************************************************************************************
          driver-class-name: com.mysql.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
  servlet:
    multipart:
      enabled: true
      max-file-size: 2000MB
      max-request-size: 2000MB



  database:
    auto-init: true
  port: 8888
  sleuth:
    log:
      slf4j:
        enabled: true
    sampler:
      # 抽样率，默认0.1
      probability: 1.0



oauth2.license.path: /data/sp/file/license
logging:
  level:
    com.sp.spengine.mapper: ERROR
    root: ERROR
    org.springframework.web.servlet.DispatcherServlet: ERROR
    org.springframework.cloud.sleuth: ERROR

csv.file.save.path: /tmp/upload/
csv.child.file.save.path: /tmp/upload//child/
del.file.path: /tmp
# csv.file.save.path: /data/sp/file/csv/

spring.cache.enabled: false
spark.store.path: /tmp/warehouse/
spark.metastore.jdbc.url: ********************************************************************************************************************************************
spark.metastore.jdbc.user: root
spark.metastore.jdbc.password: 123456
spark.master: local[16]


redis.env: dev

spring.redis.host: *************
spring.redis.port: 30379
#连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active: 50
#连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait: 3000
#连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle: 20
#连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle: 2
#连接超时时间（毫秒）
spring.redis.timeout: 5000

security:
  oauth2:
    client:
      client-id: client
      client-secret: secret
      access-token-uri: http://localhost:8888/oauth/token
      user-authorization-uri: http://localhost:8888/oauth/authorize
    resource:
      token-info-uri: http://localhost:8888/oauth/check_token
mybatis-plus:
  mapper-locations: classpath:mapper/**.xml

prx.admin.license.enabled: false
spark.conf: "{\"spark.sql.legacy.timeParserPolicy\": \"LEGACY\",\"spark.sql.parquet.datetimeRebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInWrite\": \"LEGACY\",\"spark.sql.legacy.parquet.int96RebaseModeInRead\": \"LEGACY\"}"
