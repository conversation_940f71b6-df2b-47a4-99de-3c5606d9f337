<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1tiasx4" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js (https://demo.bpmn.io)" exporterVersion="10.2.0">
  <bpmn:process id="Process_0cjmgi5" isExecutable="false">
    <bpmn:startEvent id="StartEvent_15s6tql" name="开始">
      <bpmn:outgoing>Flow_0new572</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_0zyhsyl" name="事件C">
      <bpmn:incoming>Flow_0new572</bpmn:incoming>
      <bpmn:outgoing>Flow_1rafp2d</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1rafp2d" sourceRef="Activity_0zyhsyl" targetRef="Gateway_0tj7rwv" />
    <bpmn:task id="Activity_1ipo8nu" name="事件D-1">
      <bpmn:incoming>Flow_19zvldx</bpmn:incoming>
      <bpmn:outgoing>Flow_1g51jxe</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_19zvldx" sourceRef="Gateway_0tj7rwv" targetRef="Activity_1ipo8nu" />
    <bpmn:task id="Activity_0zdexah" name="事件D-2">
      <bpmn:incoming>Flow_1ine1kh</bpmn:incoming>
      <bpmn:outgoing>Flow_1f36d0z</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1ine1kh" sourceRef="Gateway_0tj7rwv" targetRef="Activity_0zdexah" />
    <bpmn:sequenceFlow id="Flow_1g51jxe" sourceRef="Activity_1ipo8nu" targetRef="Gateway_1pss3fy" />
    <bpmn:sequenceFlow id="Flow_1f36d0z" sourceRef="Activity_0zdexah" targetRef="Gateway_1pss3fy" />
    <bpmn:task id="Activity_0s3cnof" name="事件E">
      <bpmn:incoming>Flow_0pi5ci6</bpmn:incoming>
      <bpmn:outgoing>Flow_0bl9y6a</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0pi5ci6" sourceRef="Gateway_1pss3fy" targetRef="Activity_0s3cnof" />
    <bpmn:endEvent id="Event_0slspel">
      <bpmn:incoming>Flow_0bl9y6a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0bl9y6a" sourceRef="Activity_0s3cnof" targetRef="Event_0slspel" />
    <bpmn:sequenceFlow id="Flow_0new572" sourceRef="StartEvent_15s6tql" targetRef="Activity_0zyhsyl" />
    <bpmn:exclusiveGateway id="Gateway_0tj7rwv" name="并行网关开始">
      <bpmn:incoming>Flow_1rafp2d</bpmn:incoming>
      <bpmn:outgoing>Flow_19zvldx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ine1kh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1pss3fy">
      <bpmn:incoming>Flow_1g51jxe</bpmn:incoming>
      <bpmn:incoming>Flow_1f36d0z</bpmn:incoming>
      <bpmn:outgoing>Flow_0pi5ci6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_0cjmgi5">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_15s6tql">
        <dc:Bounds x="152" y="241" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="284" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zyhsyl_di" bpmnElement="Activity_0zyhsyl">
        <dc:Bounds x="320" y="219" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ipo8nu_di" bpmnElement="Activity_1ipo8nu">
        <dc:Bounds x="640" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zdexah_di" bpmnElement="Activity_0zdexah">
        <dc:Bounds x="650" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s3cnof_di" bpmnElement="Activity_0s3cnof">
        <dc:Bounds x="960" y="210" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0slspel_di" bpmnElement="Event_0slspel">
        <dc:Bounds x="1152" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vv6bcc_di" bpmnElement="Gateway_0tj7rwv" isMarkerVisible="true">
        <dc:Bounds x="505" y="234" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="252" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fgjgum_di" bpmnElement="Gateway_1pss3fy" isMarkerVisible="true">
        <dc:Bounds x="825" y="225" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1rafp2d_di" bpmnElement="Flow_1rafp2d">
        <di:waypoint x="420" y="259" />
        <di:waypoint x="505" y="259" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19zvldx_di" bpmnElement="Flow_19zvldx">
        <di:waypoint x="530" y="234" />
        <di:waypoint x="530" y="120" />
        <di:waypoint x="640" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ine1kh_di" bpmnElement="Flow_1ine1kh">
        <di:waypoint x="530" y="284" />
        <di:waypoint x="530" y="370" />
        <di:waypoint x="650" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g51jxe_di" bpmnElement="Flow_1g51jxe">
        <di:waypoint x="740" y="120" />
        <di:waypoint x="850" y="120" />
        <di:waypoint x="850" y="225" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f36d0z_di" bpmnElement="Flow_1f36d0z">
        <di:waypoint x="750" y="370" />
        <di:waypoint x="850" y="370" />
        <di:waypoint x="850" y="275" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pi5ci6_di" bpmnElement="Flow_0pi5ci6">
        <di:waypoint x="875" y="250" />
        <di:waypoint x="960" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bl9y6a_di" bpmnElement="Flow_0bl9y6a">
        <di:waypoint x="1060" y="250" />
        <di:waypoint x="1152" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0new572_di" bpmnElement="Flow_0new572">
        <di:waypoint x="188" y="259" />
        <di:waypoint x="320" y="259" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
