/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
  id 'java'
  id 'org.springframework.boot' version '2.3.2.RELEASE'
}

group = 'com.prx'
version = '0.0.1'

repositories {
  mavenLocal()
  mavenCentral()
  maven { url 'http://121.37.155.119:8081/repository/maven-public/' }
  maven { url 'https://jindodata-binary.oss-cn-shanghai.aliyuncs.com/mvn-repo/' }
}
dependencies {
  implementation platform("org.springframework.boot:spring-boot-dependencies:2.3.2.RELEASE")
  implementation project(':proxverse-data')
  implementation project(':proxverse-execution')
  implementation project(':proxverse-data-core')
  implementation project(':proxverse-data-merge')
  implementation project(':proxverse-engine')
  implementation project(':proxverse-oauth2')
  implementation project(':proxverse-process-manage')
  implementation project(':backend')
  implementation project(':proxverse-job')
  implementation 'com.sp:proxverse-spark-common:1.0.0'
  implementation 'com.sp:starry-core:0.0.9'
  implementation 'org.xerial.snappy:snappy-java:1.1.8.4'
  implementation ('org.apache.spark:spark-core_2.12:3.3.1-prx-0.0.2') {
    exclude group: 'org.apache.hadoop', module: 'hadoop-client-api'
    exclude group: 'org.apache.hadoop', module: 'hadoop-client-runtime'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-core'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-1.2-api'
    exclude group: 'org.glassfish.jersey.core', module: 'jersey-client'
    exclude group: 'org.glassfish.jersey.core', module: 'jersey-common'
    exclude group: 'org.glassfish.jersey.core', module: 'jersey-server'
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j-impl'
  }
  implementation 'com.h2database:h2:2.1.214'
  implementation 'org.apache.derby:derby:10.14.2.0'
  implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.107'
  implementation 'org.springframework.security:spring-security-config:5.3.3.RELEASE'
  implementation 'org.springframework:spring-context:5.2.8.RELEASE'
  implementation 'org.springframework:spring-tx:5.2.8.RELEASE'
  implementation 'org.mybatis:mybatis-spring:2.1.2'
  implementation 'org.springframework.boot:spring-boot-starter:2.3.2.RELEASE'
  implementation 'org.springframework.boot:spring-boot:2.3.2.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-logging:2.3.2.RELEASE'
  implementation 'org.springframework.boot:spring-boot-starter-jdbc:2.3.2.RELEASE'
  implementation 'org.codehaus.janino:janino:3.0.16'
}

bootJar {
  archiveClassifier.set('exec')
    // This configuration goes through each dependency and only includes those that start with 'proxverse'
    mainClassName = 'com.sp.web.main.MainApplication' // Replace with your actual main class
    doFirst {
      classpath.each { file ->
        println file.name
      }
      classpath = classpath.filter {
        (it.name.startsWith("proxverse") || it.name.startsWith("backend") ||  it.name.startsWith("main")
            )
      }
    }
}


description = 'proxverse-main'

configurations {
  all {
    exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j-impl'
  }
}

task copyDependency(type: Copy) {
  // 配置从哪里复制
  from configurations.runtimeClasspath
  // 配置复制到哪里
  into 'build/extend'
  // 可以使用 include 和 exclude 来过滤文件
  exclude 'proxverse*.jar'
  exclude 'backend.jar'
  exclude 'jindo-core-macos-11_0-aarch64*'
  exclude 'h2*'
  exclude '*log4j-slf4j-impl*'
}

// 你可能还想要在构建 JAR 文件之前执行这个任务
jar.dependsOn copyDependency


ext {
  set('springBootVersion', '2.3.2.RELEASE')
}

configurations.all {
  resolutionStrategy {
    eachDependency { DependencyResolveDetails details ->
      println(details.requested.group)
      if (details.requested.group == 'org.springframework.boot') {
        details.useVersion '2.3.2.RELEASE'
      }
      if (details.requested.group == 'org.codehaus.janino' && details.requested.name == 'janino') {
        details.useVersion '3.0.16'
      }
      if (details.requested.group == 'org.codehaus.janino' && details.requested.name == 'janino') {
        details.useVersion '3.0.16'
      }
      if ( details.requested.name == 'log4j-1.2-api') {
        details.useVersion '2.17.0'
      }
      if ( details.requested.name == 'log4j-api') {
        details.useVersion '2.17.0'
      }
      if ( details.requested.name == 'log4j-core') {
        details.useVersion '2.17.0'
      }
      if ( details.requested.name == 'guava') {
        details.useVersion '24.1.1-jre'
      }
    }
  }
}
