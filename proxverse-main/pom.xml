<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sp</groupId>
    <version>1.0.0</version>
    <artifactId>proxverse-project</artifactId>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>proxverse-main</artifactId>
  <version>1.0.0</version>
  <description>Demo project for Spring Boot</description>
  <properties>
    <java.version>1.8</java.version>
    <velocity.version>2.1</velocity.version>
    <caffeine.version>2.8.2</caffeine.version>
  </properties>
  <dependencies>


    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-aws</artifactId>
      <version>3.3.4</version> <!-- 与你的 Hadoop 版本一致 -->
    </dependency>

    <!-- AWS Java SDK（需匹配 Hadoop 版本要求） -->
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-bundle</artifactId>
      <version>1.12.262</version> <!-- Hadoop 3.3.x 推荐版本 -->
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>


    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-data</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-execution</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-data-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-data-merge</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-engine</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-oauth2</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-process-manage</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>backend</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-job</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>proxverse-spark-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sp</groupId>
      <artifactId>starry-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.xerial.snappy</groupId>
      <artifactId>snappy-java</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-core_2.12</artifactId>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.1.214</version>
    </dependency>
    <dependency>
      <groupId>org.apache.derby</groupId>
      <artifactId>derby</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>proxverse-project</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <!--            <plugin>-->
      <!--                <groupId>org.apache.maven.plugins</groupId>-->
      <!--                <artifactId>maven-jar-plugin</artifactId>-->
      <!--                <configuration>-->
      <!--                    <archive>-->
      <!--                        <manifestEntries>-->
      <!--                            <Bundle-SymbolicName>org.datanucleus.store.rdbms;org.datanucleus;singleton:=true</Bundle-SymbolicName>-->
      <!--                            <Premain-Class>org.datanucleus.enhancer.DataNucleusClassFileTransformer</Premain-Class>-->
      <!--                        </manifestEntries>-->
      <!--                    </archive>-->
      <!--                </configuration>-->
      <!--            </plugin>-->

      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <fork>true</fork>
          <addResources>true</addResources>
          <classifier>exec</classifier>
          <includes>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-data</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-data-merge</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-common</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-engine</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-oauth2</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-execution</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-service-interface</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-web-config</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>backend</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-job</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-data-core</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-pql</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-spark-common</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-server</artifactId>
            </include>
            <include>
              <groupId>com.sp</groupId>
              <artifactId>proxverse-process-manage</artifactId>
            </include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>copy-lib</id>
            <phase>package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>target/libs</outputDirectory>
              <excludeTransitive>false</excludeTransitive>
              <stripVersion>false</stripVersion>
              <includeScope>runtime</includeScope>
              <excludeArtifactIds>
                jindo-core-macos-11_0-aarch64,
                h2,
                proxverse-data,
                proxverse-common,
                proxverse-web-config,
                proxverse-service-interface,
                proxverse-server,
                proxverse-execution,
                proxverse-data-core,
                proxverse-pql,
                proxverse-data-merge,
                proxverse-engine,
                proxverse-oauth2,
                proxverse-process-manage,
                proxverse-job,
                proxverse-spark-common,
                backend
              </excludeArtifactIds>

            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-appCtx</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/libs/
              </outputDirectory>
              <overwrite>true</overwrite>
              <resources>
                <resource>
                  <directory>
                    ${project.build.directory}/../../proxverse-web-config/src/main/resources/lib/
                  </directory>
                  <includes>
                    <include>
                      licmgr-keygen-1.0-SNAPSHOT-standalone.jar
                    </include>
                    <include>
                      licmgr-keymgr-1.0-SNAPSHOT-standalone.jar
                    </include>
                    <include>
                      mysql-connector-java-5.1.49.jar
                    </include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>

          <execution>
            <id>copy-skywalking</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>target/libs/skywalking-apm</outputDirectory>
              <resources>
                <resource>
                  <directory>
                    ${project.basedir}/src/main/resources/skywalking-apm
                  </directory>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-jdbc</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>target/libs/</outputDirectory>
              <resources>
                <resource>
                  <directory>
                    ${project.basedir}/src/main/resources/jdbc
                  </directory>
                </resource>
              </resources>
            </configuration>
          </execution>

        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <!--<plugin>-->
      <!--<groupId>pl.project13.maven</groupId>-->
      <!--<artifactId>git-commit-id-plugin</artifactId>-->
      <!--<executions>-->
      <!--<execution>-->
      <!--<goals>-->
      <!--<goal>revision</goal>-->
      <!--</goals>-->
      <!--</execution>-->
      <!--</executions>-->
      <!--<configuration>-->
      <!--<verbose>true</verbose>-->
      <!--<dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>-->
      <!--<generateGitPropertiesFile>true</generateGitPropertiesFile>-->
      <!--<generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties-->
      <!--</generateGitPropertiesFilename>-->
      <!--</configuration>-->
      <!--</plugin>-->
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>scala-compile-first</id>
            <phase>process-resources</phase>
            <goals>
              <goal>add-source</goal>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>scala-test-compile</id>
            <phase>process-test-resources</phase>
            <goals>
              <goal>add-source</goal>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <args>
            <!-- 编译时使用 libs 目录下的 jar 包，通过 mvn scala:help 查看说明 -->
            <arg>-extdirs</arg>
            <arg>${project.basedir}/libs</arg>
          </args>
          <scalaVersion>2.12.15</scalaVersion>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
